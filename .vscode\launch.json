{"version": "0.2.0", "configurations": [{"name": "🚀 Launch DM's <PERSON><PERSON><PERSON> Backend", "type": "node", "request": "launch", "program": "${workspaceFolder}/backend/server.js", "cwd": "${workspaceFolder}/backend", "env": {"NODE_ENV": "development"}, "envFile": "${workspaceFolder}/backend/.env", "console": "integratedTerminal", "restart": true, "runtimeExecutable": "node", "skipFiles": ["<node_internals>/**"], "outputCapture": "std", "internalConsoleOptions": "neverOpen"}, {"name": "🔧 Debug DM's <PERSON><PERSON><PERSON>end", "type": "node", "request": "launch", "program": "${workspaceFolder}/backend/server.js", "cwd": "${workspaceFolder}/backend", "env": {"NODE_ENV": "development", "DEBUG": "*"}, "envFile": "${workspaceFolder}/backend/.env", "console": "integratedTerminal", "restart": true, "runtimeExecutable": "node", "runtimeArgs": ["--inspect"], "skipFiles": ["<node_internals>/**"], "outputCapture": "std", "internalConsoleOptions": "neverOpen", "stopOnEntry": false, "smartStep": true}, {"name": "🎨 Launch DM's Whisper Frontend", "type": "node", "request": "launch", "program": "${workspaceFolder}/frontend/node_modules/.bin/vite", "args": ["dev", "--host", "0.0.0.0"], "cwd": "${workspaceFolder}/frontend", "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "restart": false, "runtimeExecutable": "node", "skipFiles": ["<node_internals>/**"], "outputCapture": "std", "internalConsoleOptions": "neverOpen"}, {"name": "🌐 Launch Full Stack (Backend + Frontend)", "type": "node", "request": "launch", "program": "${workspaceFolder}/scripts/launch-fullstack.js", "cwd": "${workspaceFolder}", "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "restart": false, "runtimeExecutable": "node", "skipFiles": ["<node_internals>/**"], "outputCapture": "std", "internalConsoleOptions": "neverOpen"}], "compounds": [{"name": "🚀 Launch Complete DM's Whisper System", "configurations": ["🚀 Launch DM's <PERSON><PERSON><PERSON> Backend", "🎨 Launch DM's Whisper Frontend"], "stopAll": true, "presentation": {"hidden": false, "group": "DM's <PERSON><PERSON><PERSON>", "order": 1}}, {"name": "🔧 Debug Complete System", "configurations": ["🔧 Debug DM's <PERSON><PERSON><PERSON>end", "🎨 Launch DM's Whisper Frontend"], "stopAll": true, "presentation": {"hidden": false, "group": "DM's <PERSON><PERSON><PERSON>", "order": 2}}]}