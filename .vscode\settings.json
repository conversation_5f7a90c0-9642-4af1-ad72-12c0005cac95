{"typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "svelte.enable-ts-plugin": true, "svelte.plugin.typescript.enable": true, "svelte.plugin.typescript.diagnostics.enable": true, "files.associations": {"*.svelte": "svelte"}, "emmet.includeLanguages": {"svelte": "html"}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/.svelte-kit": true, "**/build": true, "**/coverage": true}, "files.exclude": {"**/.git": true, "**/.DS_Store": true, "**/node_modules": true, "**/.svelte-kit": true}, "files.watcherExclude": {"**/node_modules/**": true, "**/.svelte-kit/**": true, "**/dist/**": true, "**/build/**": true}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.organizeImports": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "[svelte]": {"editor.defaultFormatter": "svelte.svelte-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "eslint.workingDirectories": ["frontend", "backend"], "terminal.integrated.cwd": "${workspaceFolder}", "terminal.integrated.defaultProfile.windows": "PowerShell", "debug.console.fontSize": 14, "debug.console.lineHeight": 20, "workbench.colorCustomizations": {"activityBar.background": "#1e3c72", "activityBar.foreground": "#ffffff", "statusBar.background": "#2a5298", "statusBar.foreground": "#ffffff", "titleBar.activeBackground": "#1e3c72", "titleBar.activeForeground": "#ffffff"}, "workbench.iconTheme": "material-icon-theme", "explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"package.json": "package-lock.json, yarn.lock, pnpm-lock.yaml", ".env": ".env.*", "*.js": "$(capture).js.map", "*.ts": "$(capture).js, $(capture).js.map, $(capture).d.ts", "*.svelte": "$(capture).svelte.d.ts"}, "git.ignoreLimitWarning": true, "git.autofetch": true, "extensions.ignoreRecommendations": false, "todo-tree.general.tags": ["TODO", "FIXME", "HACK", "NOTE", "BUG"], "todo-tree.highlights.defaultHighlight": {"icon": "alert", "type": "text", "foreground": "#ffffff", "background": "#ffcc00", "opacity": 50, "iconColour": "#ffcc00"}, "todo-tree.highlights.customHighlight": {"TODO": {"icon": "check", "background": "#ffcc00"}, "FIXME": {"icon": "bug", "background": "#f44336"}, "HACK": {"icon": "alert", "background": "#ff9800"}, "NOTE": {"icon": "note", "background": "#2196f3"}, "BUG": {"icon": "bug", "background": "#f44336"}}}