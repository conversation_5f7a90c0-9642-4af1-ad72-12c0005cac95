{"version": "2.0.0", "tasks": [{"label": "📦 Install Backend Dependencies", "type": "shell", "command": "npm", "args": ["install"], "options": {"cwd": "${workspaceFolder}/backend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "📦 Install Frontend Dependencies", "type": "shell", "command": "npm", "args": ["install"], "options": {"cwd": "${workspaceFolder}/frontend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "📦 Install All Dependencies", "dependsOrder": "sequence", "dependsOn": ["📦 Install Backend Dependencies", "📦 Install Frontend Dependencies"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}}, {"label": "🚀 Start Backend", "type": "shell", "command": "npm", "args": ["run", "dev"], "options": {"cwd": "${workspaceFolder}/backend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "dedicated", "showReuseMessage": true, "clear": true}, "isBackground": true, "problemMatcher": {"pattern": {"regexp": "^(.*)$", "file": 1, "location": 2, "message": 3}, "background": {"activeOnStart": true, "beginsPattern": "^.*starting.*$", "endsPattern": "^.*DM's <PERSON><PERSON><PERSON>end running on port.*$"}}}, {"label": "🎨 Start Frontend", "type": "shell", "command": "npm", "args": ["run", "dev"], "options": {"cwd": "${workspaceFolder}/frontend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "dedicated", "showReuseMessage": true, "clear": true}, "isBackground": true, "problemMatcher": {"pattern": {"regexp": "^(.*)$", "file": 1, "location": 2, "message": 3}, "background": {"activeOnStart": true, "beginsPattern": "^.*VITE.*$", "endsPattern": "^.*Local:.*5173.*$"}}}, {"label": "🌐 Start Full Stack", "type": "shell", "command": "node", "args": ["scripts/launch-fullstack.js"], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "dedicated", "showReuseMessage": true, "clear": true}, "isBackground": true, "problemMatcher": []}, {"label": "🧹 Clean Backend", "type": "shell", "command": "rm", "args": ["-rf", "node_modules", "package-lock.json"], "options": {"cwd": "${workspaceFolder}/backend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "🧹 Clean Frontend", "type": "shell", "command": "rm", "args": ["-rf", "node_modules", "package-lock.json", ".svelte-kit"], "options": {"cwd": "${workspaceFolder}/frontend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "🧹 Clean All", "dependsOrder": "parallel", "dependsOn": ["🧹 Clean Backend", "🧹 Clean Frontend"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}}, {"label": "🔧 Setup Environment", "type": "shell", "command": "cp", "args": ["backend/.env.example", "backend/.env"], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "🏗️ Full Setup", "dependsOrder": "sequence", "dependsOn": ["🔧 Setup Environment", "📦 Install All Dependencies"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared", "showReuseMessage": true, "clear": true}}]}