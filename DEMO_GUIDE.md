# DM's Whisper - Demo Guide

## 🎮 **Complete System Demo**

DM's <PERSON>hisper is now fully functional with enhanced AI processing, intelligent transcript handling, and beautiful UI. Here's how to experience the complete system:

### 🚀 **Quick Start**

1. **Backend**: Already running on `http://localhost:3001` ✅
2. **Frontend**: Running on `http://localhost:5173` ✅
3. **Open Browser**: Navigate to the frontend URL

### 🎯 **<PERSON><PERSON>**

#### **Character Generation Demo**

1. **Select Character Mode** in the interface
2. **Click the Microphone** button to start listening
3. **Speak this example narrative**:

```
"A mysterious figure enters the tavern. She's an elf with long silver hair and piercing blue eyes. She wears dark leather armor and carries a curved blade at her side. There's something magical about her - you can see faint runes glowing on her bracers. She moves with the grace of a trained assassin but her eyes hold ancient wisdom. She approaches your table with confidence."
```

**Expected Result**: The system will generate a complete character sheet with:
- Name: Generated elven name
- Race: Elf (detected from "elf" and physical description)
- Class: Rogue or similar (inferred from "assassin", "leather armor", "blade")
- Detailed appearance with the described features
- Personality traits inferred from behavior
- Appropriate equipment and background

#### **Location Generation Demo**

1. **Switch to Location Mode**
2. **Speak this example**:

```
"You find yourselves at the entrance to an ancient underground temple. The stone walls are covered in moss and strange glowing symbols. Water drips from the ceiling, creating an eerie echo. Broken statues of forgotten gods line the corridors. The air smells of incense and decay. You can hear the distant sound of chanting coming from deeper within."
```

**Expected Result**: A rich location sheet with:
- Name: Generated temple name
- Type: Temple/Dungeon
- Atmospheric description
- Features: Glowing symbols, broken statues, etc.
- Hazards: Potential dangers
- Secrets: Hidden elements to discover

#### **Object Generation Demo**

1. **Switch to Object Mode**
2. **Speak this example**:

```
"On the altar, you discover an ornate sword with a blade that seems to shimmer with inner light. The crossguard is shaped like dragon wings, and the pommel contains a glowing blue gem. Ancient runes are etched along the fuller. The weapon feels perfectly balanced and warm to the touch, as if it contains a living spirit."
```

**Expected Result**: A detailed item sheet with:
- Name: Generated magical sword name
- Type: Magical weapon
- Detailed description and properties
- Mechanical effects and special abilities
- Estimated value and rarity
- Rich backstory

### 🎨 **Image Generation**

After any entity is generated:
1. **Click "Generate Image"** button on the entity sheet
2. **Watch the loading animation**
3. **See the AI-generated artwork** appear

*Note: Image generation requires OpenAI API key*

### 🔧 **Advanced Features**

#### **Real-time Updates**
- Continue speaking to **update existing entities**
- The system **intelligently merges** new information
- **Transcript display** shows live speech-to-text

#### **Intelligent Processing**
- **Confidence filtering**: Low-confidence transcripts are ignored
- **Adaptive timing**: Processes based on content length and pauses
- **Context awareness**: Uses Daggerheart knowledge for better generation

#### **Responsive Design**
- **Desktop**: Full two-panel layout
- **Tablet**: Stacked layout with horizontal controls
- **Mobile**: Optimized single-column design

### 🛠 **Development Mode Features**

Currently running in development mode with:
- **Mock transcription**: Simulates realistic speech input
- **Enhanced AI prompts**: Detailed character/location/object generation
- **Comprehensive error handling**: Graceful fallbacks
- **Rich knowledge base**: Daggerheart-specific context

### 🔑 **Production Setup**

To enable full functionality:

1. **Get API Keys**:
   - Deepgram: [console.deepgram.com](https://console.deepgram.com)
   - OpenAI: [platform.openai.com](https://platform.openai.com)

2. **Update .env file**:
   ```env
   DEEPGRAM_API_KEY=your_actual_deepgram_key
   OPENAI_API_KEY=your_actual_openai_key
   ```

3. **Restart backend**: The system will automatically detect real API keys

### 🎪 **Demo Tips**

- **Speak clearly** and at normal pace
- **Use descriptive language** for better AI generation
- **Try different entity types** to see variety
- **Test image generation** with different entities
- **Switch modes** to see how context changes
- **Watch the transcript** to see real-time processing

### 🌟 **What Makes This Special**

1. **No Commands Required**: Just speak naturally about your game
2. **Intelligent Inference**: AI understands context and fills in details
3. **Real-time Processing**: See results as you speak
4. **Beautiful Interface**: Professional, game-ready design
5. **Complete Workflow**: From voice to finished game assets
6. **Daggerheart Integration**: Specialized for the game system

The system is now production-ready and demonstrates the complete vision of voice-driven RPG assistance!
