# DM's Whisper - <PERSON><PERSON><PERSON> DM Assistant Tool

A real-time voice-activated web application that assists Daggerheart Dungeon Masters by listening to their narration and dynamically generating character, location, and object sheets without requiring direct commands.

## Features

- **Voice-Activated Interface**: Listens to DM narration through microphone
- **Real-Time Transcription**: Uses Deepgram API for low-latency speech-to-text
- **AI-Powered Inference**: Leverages OpenAI GPT-4o to extract character, location, and object data from narrative
- **Dynamic Sheet Generation**: Automatically creates and updates game sheets based on spoken descriptions
- **Image Generation**: Manual DALL-E 3 integration for visual representation of generated entities
- **Single-Page Interface**: No-scroll design with mode switching for different entity types
- **RAG System**: ChromaDB-powered knowledge base for Daggerheart game context

## Architecture

### Frontend (SvelteKit)
- Single-page application with TypeScript
- Real-time audio capture and streaming
- Socket.IO client for bidirectional communication
- Reactive UI components for dynamic sheet updates

### Backend (Node.js + Express)
- Socket.IO server for real-time communication
- Deepgram integration for speech transcription
- OpenAI API integration (GPT-4o + DALL-E 3)
- ChromaDB for RAG-based context retrieval

## Project Structure

```
├── frontend/          # SvelteKit frontend application
├── backend/           # Node.js backend server
│   ├── src/
│   │   ├── services/  # External API integrations
│   │   ├── models/    # Data models and schemas
│   │   ├── utils/     # Utility functions
│   │   └── routes/    # API routes
│   ├── server.js      # Main server file
│   └── package.json
└── README.md
```

## Setup Instructions

### Prerequisites
- Node.js 18+ 
- npm or yarn
- API keys for:
  - Deepgram (speech transcription)
  - OpenAI (GPT-4o and DALL-E 3)

### Installation

1. Clone the repository
2. Set up backend:
   ```bash
   cd backend
   npm install
   cp .env.example .env
   # Edit .env with your API keys
   ```

3. Set up frontend:
   ```bash
   cd frontend
   npm install
   ```

### Running the Application

1. Start the backend server:
   ```bash
   cd backend
   npm run dev
   ```

2. Start the frontend development server:
   ```bash
   cd frontend
   npm run dev
   ```

3. Open your browser to `http://localhost:5173`

## Usage

1. Select a mode (Character, Location, or Object generation)
2. Click the microphone button to start listening
3. Begin narrating your game scenario
4. Watch as the tool automatically generates and updates entity sheets
5. Click "Generate Image" on any sheet to create visual representations

## Technology Stack

- **Frontend**: SvelteKit, TypeScript, Socket.IO Client
- **Backend**: Node.js, Express, Socket.IO
- **AI Services**: OpenAI GPT-4o, DALL-E 3, Deepgram
- **Database**: ChromaDB (vector database for RAG)
- **Real-time Communication**: WebSockets via Socket.IO

## Development Status

This project is currently in active development. See the task list for current progress and upcoming features.
