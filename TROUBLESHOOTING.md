# 🔧 DM's Whisper Troubleshooting Guide

## 🎯 **Current Status**
- ✅ **Backend**: Running on port 3001 with real API keys
- ✅ **Deepgram**: API key active, WebM audio format configured
- ✅ **OpenAI**: API key active, GPT-4o and DALL-E 3 ready
- ✅ **Socket.IO**: Real-time communication established
- ⚠️ **ChromaDB**: Using development mode (expected)

## 🎤 **Testing Voice Transcription**

### **Step-by-Step Test:**
1. **Open Frontend**: Navigate to `http://localhost:5173`
2. **Check Connection**: Look for green "Connected" status
3. **Select Mode**: Choose "Character" mode
4. **Start Recording**: Click the microphone button (should turn red)
5. **Speak Clearly**: Say something like "A tall elf warrior with a silver sword"
6. **Watch Console**: Check browser developer tools for any errors

### **What to Look For:**
- **Microphone Button**: Should turn red when recording
- **Transcript Area**: Should show "Transcript will appear here..."
- **Browser Console**: Check for audio permission requests
- **Backend Logs**: Watch for audio data and Deepgram responses

## 🔍 **Debugging Steps**

### **If No Audio is Captured:**
1. **Check Microphone Permissions**: Browser should ask for microphone access
2. **Try Different Browser**: Chrome/Edge work best for WebRTC
3. **Check Audio Input**: Ensure microphone is working in other apps

### **If No Transcription Appears:**
1. **Check Backend Logs**: Look for "Sending X bytes of audio to Deepgram"
2. **Verify API Key**: Ensure Deepgram key is valid and has credits
3. **Audio Format**: Backend should show "Deepgram Results received"

### **If No Character Generation:**
1. **Check Transcript Length**: Need at least 10 words for processing
2. **Wait for Processing**: AI generation takes 2-5 seconds
3. **Check OpenAI Key**: Verify API key has credits and access

## 🎮 **Test Scenarios**

### **Character Test:**
```
"A mysterious elven ranger enters the tavern. She has long silver hair and carries a bow made of white wood. Her green cloak shows signs of long travel."
```

**Expected Result**: Character sheet with elf race, ranger class, detailed appearance

### **Location Test:**
```
"You find yourselves in an ancient library with towering bookshelves reaching into darkness. Dust motes dance in shafts of light from stained glass windows."
```

**Expected Result**: Location sheet with library type, atmospheric details

### **Object Test:**
```
"On the pedestal sits an ornate sword with a blade that glows with blue light. The crossguard is shaped like dragon wings and runes cover the fuller."
```

**Expected Result**: Object sheet with magical weapon details

## 🚨 **Common Issues**

### **"Microphone Access Denied"**
- **Solution**: Refresh page and allow microphone access
- **Chrome**: Click lock icon in address bar → Allow microphone

### **"Failed to Connect to Server"**
- **Solution**: Ensure backend is running on port 3001
- **Check**: `curl http://localhost:3001/health`

### **"No Transcription Generated"**
- **Check**: Deepgram API key validity and credits
- **Try**: Speaking louder and more clearly
- **Verify**: Audio format compatibility

### **"Processing Error"**
- **Check**: OpenAI API key validity and credits
- **Wait**: Processing can take a few seconds
- **Retry**: Try with simpler descriptions first

## 📊 **Expected Backend Logs**

When working correctly, you should see:
```
Deepgram live transcription started for socket: [ID]
Sending [X] bytes of audio to Deepgram for socket [ID]
Deepgram Results received for socket [ID]: {...}
Sending transcript to client: [transcript text]
Processing transcript for character: "[text]..."
Successfully generated character data for client [ID]
```

## 🔧 **Quick Fixes**

### **Restart Everything:**
```bash
# Backend
cd backend
npm run dev

# Frontend (new terminal)
cd frontend  
npm run dev
```

### **Clear Browser Cache:**
- Hard refresh: `Ctrl+Shift+R`
- Clear site data in developer tools

### **Test API Keys:**
```bash
# Test Deepgram
curl -X POST "https://api.deepgram.com/v1/listen" \
  -H "Authorization: Bearer YOUR_DEEPGRAM_KEY" \
  -H "Content-Type: audio/wav" \
  --data-binary @test.wav

# Test OpenAI
curl -X POST "https://api.openai.com/v1/chat/completions" \
  -H "Authorization: Bearer YOUR_OPENAI_KEY" \
  -H "Content-Type: application/json" \
  -d '{"model":"gpt-4o","messages":[{"role":"user","content":"Hello"}]}'
```

The system should now work with real voice transcription and AI generation!
