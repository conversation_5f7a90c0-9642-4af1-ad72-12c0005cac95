# VS Code Setup for DM's Whisper

## 🚀 **Quick Launch Guide**

The project now includes comprehensive VS Code configuration for easy development and debugging.

### **One-Click Launch Options**

1. **Press `F5`** or go to **Run and Debug** panel
2. **Select from available configurations**:
   - 🚀 **Launch Complete DM's Whisper System** - Starts both backend and frontend
   - 🔧 **Debug Complete System** - Starts with backend debugging enabled
   - 🚀 **Launch DM's Whisper Backend** - Backend only
   - 🎨 **Launch DM's Whisper Frontend** - Frontend only
   - 🔧 **Debug DM's Whisper Backend** - Backend with full debugging

### **Task Runner Options**

Press `Ctrl+Shift+P` and type "Tasks: Run Task" to access:

- 📦 **Install All Dependencies** - Sets up both backend and frontend
- 🌐 **Start Full Stack** - Launches coordinated backend + frontend
- 🏗️ **Full Setup** - Complete project setup from scratch
- 🧹 **Clean All** - Removes node_modules and rebuilds
- 🔧 **Setup Environment** - Creates .env file from template

## 🛠 **Development Features**

### **Debugging Capabilities**
- **Backend Debugging**: Full Node.js debugging with breakpoints
- **Error Handling**: Automatic error detection and reporting
- **Live Reload**: Both backend (nodemon) and frontend (Vite) hot reload
- **Integrated Terminal**: All output in VS Code terminal

### **Code Quality**
- **Auto-formatting**: Prettier integration for consistent code style
- **TypeScript Support**: Full IntelliSense for frontend TypeScript
- **Svelte Support**: Syntax highlighting and component intelligence
- **ESLint Integration**: Real-time code quality checking

### **File Organization**
- **File Nesting**: Related files grouped together (package.json + lock files)
- **Smart Search**: Excludes node_modules and build directories
- **Icon Theme**: Material icons for better file recognition

## 🎯 **Recommended Workflow**

### **First Time Setup**
1. Open project in VS Code
2. Install recommended extensions (VS Code will prompt)
3. Run task: **🏗️ Full Setup**
4. Launch: **🚀 Launch Complete DM's Whisper System**

### **Daily Development**
1. Open VS Code
2. Press `F5` → **🚀 Launch Complete DM's Whisper System**
3. Start coding with full debugging support

### **Debugging Issues**
1. Press `F5` → **🔧 Debug Complete System**
2. Set breakpoints in backend code
3. Use integrated terminal for logs

## 📋 **Required VS Code Extensions**

The project works best with these extensions (auto-recommended):

### **Essential**
- **Svelte for VS Code** - Svelte component support
- **TypeScript and JavaScript Language Features** - Built-in TS support
- **Prettier - Code formatter** - Auto-formatting
- **ESLint** - Code quality

### **Recommended**
- **Material Icon Theme** - Better file icons
- **GitLens** - Enhanced Git integration
- **Thunder Client** - API testing (for backend endpoints)
- **Todo Tree** - Track TODO comments
- **Error Lens** - Inline error display

## 🔧 **Configuration Details**

### **Launch Configurations**
- **Backend**: Runs with nodemon for auto-restart
- **Frontend**: Runs Vite dev server with hot reload
- **Debug Mode**: Enables Node.js inspector for breakpoints
- **Environment**: Automatically loads .env files

### **Task Automation**
- **Dependency Management**: Automated npm install for both projects
- **Environment Setup**: Auto-creates .env from template
- **Clean Operations**: Removes build artifacts and dependencies
- **Coordinated Launch**: Starts backend first, then frontend

### **Editor Settings**
- **Auto-save**: Formats code on save
- **Import Organization**: Automatically organizes imports
- **TypeScript**: Enhanced IntelliSense and error checking
- **Svelte**: Full component support with syntax highlighting

## 🎨 **Custom Theme**

The workspace includes a custom color theme matching DM's Whisper branding:
- **Activity Bar**: Deep blue (#1e3c72)
- **Status Bar**: Gradient blue (#2a5298)
- **Title Bar**: Matching project colors

## 🚨 **Troubleshooting**

### **Port Already in Use**
- The launcher automatically detects port conflicts
- Use **🧹 Clean All** task to reset everything

### **Dependencies Issues**
- Run **📦 Install All Dependencies** task
- Check terminal output for specific errors

### **Environment Variables**
- Ensure `.env` file exists in backend directory
- Use **🔧 Setup Environment** task to create from template

### **Debugging Not Working**
- Ensure you're using **🔧 Debug** configurations
- Check that backend is running on port 3001
- Verify no other Node.js processes are interfering

## 🎯 **Pro Tips**

1. **Use Compound Configurations**: Launch both backend and frontend simultaneously
2. **Set Breakpoints**: Debug backend API calls in real-time
3. **Use Tasks**: Automate common operations like dependency installation
4. **Monitor Terminal**: Watch for real-time logs and errors
5. **Hot Reload**: Changes automatically refresh both backend and frontend

The VS Code setup provides a professional development environment that makes working with DM's Whisper efficient and enjoyable!
