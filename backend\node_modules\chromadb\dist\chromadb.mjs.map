{"version": 3, "sources": ["../src/deno.ts", "../src/types.ts", "../../../node_modules/.pnpm/@hey-api+client-fetch@0.10.0_@hey-api+openapi-ts@0.67.3_typescript@5.8.3_/node_modules/@hey-api/client-core/src/auth.ts", "../../../node_modules/.pnpm/@hey-api+client-fetch@0.10.0_@hey-api+openapi-ts@0.67.3_typescript@5.8.3_/node_modules/@hey-api/client-core/src/bodySerializer.ts", "../../../node_modules/.pnpm/@hey-api+client-fetch@0.10.0_@hey-api+openapi-ts@0.67.3_typescript@5.8.3_/node_modules/@hey-api/client-core/src/pathSerializer.ts", "../../../node_modules/.pnpm/@hey-api+client-fetch@0.10.0_@hey-api+openapi-ts@0.67.3_typescript@5.8.3_/node_modules/@hey-api/client-fetch/src/utils.ts", "../../../node_modules/.pnpm/@hey-api+client-fetch@0.10.0_@hey-api+openapi-ts@0.67.3_typescript@5.8.3_/node_modules/@hey-api/client-fetch/src/client.ts", "../src/api/client.gen.ts", "../src/api/sdk.gen.ts", "../src/errors.ts", "../src/utils.ts", "../src/embedding-function.ts", "../src/collection-configuration.ts", "../src/collection.ts", "../src/next.ts", "../src/chroma-fetch.ts", "../src/admin-client.ts", "../src/chroma-client.ts", "../src/cloud-client.ts"], "sourcesContent": ["// Deno compatibility patch for @hey-api/client-fetch\n// This must be imported before any @hey-api/client-fetch code\n\nif (typeof (globalThis as any).Deno !== \"undefined\") {\n  // Store the original Request constructor\n  const OriginalRequest = globalThis.Request;\n\n  // Create a patched Request constructor that strips Deno-incompatible properties\n  const PatchedRequest = function (\n    input: RequestInfo | URL,\n    init?: RequestInit,\n  ) {\n    if (init && typeof init === \"object\") {\n      const cleanInit = { ...init };\n      if (\"client\" in cleanInit) {\n        delete (cleanInit as any).client;\n      }\n      return new OriginalRequest(input, cleanInit);\n    }\n    return new OriginalRequest(input, init);\n  } as any;\n\n  // Copy over static properties and prototype\n  Object.setPrototypeOf(PatchedRequest, OriginalRequest);\n  Object.defineProperty(PatchedRequest, \"prototype\", {\n    value: OriginalRequest.prototype,\n    writable: false,\n  });\n\n  // Replace the global Request constructor\n  globalThis.Request = PatchedRequest;\n}\n", "import { GetUserIdentityResponse, Include } from \"./api\";\n\n/**\n * User identity information including tenant and database access.\n */\nexport type UserIdentity = GetUserIdentityResponse;\n\n/**\n * Metadata that can be associated with a collection.\n * Values must be boolean, number, or string types.\n */\nexport type CollectionMetadata = Record<\n  string,\n  boolean | number | string | null\n>;\n\n/**\n * Metadata that can be associated with individual records.\n * Values must be boolean, number, or string types.\n */\nexport type Metadata = Record<string, boolean | number | string | null>;\n\n/**\n * Base interface for record sets containing optional fields.\n */\nexport interface BaseRecordSet {\n  /** Array of embedding vectors */\n  embeddings?: number[][];\n  /** Array of metadata objects */\n  metadatas?: Metadata[];\n  /** Array of document text content */\n  documents?: string[];\n  /** Array of URIs/URLs */\n  uris?: string[];\n}\n\nexport const baseRecordSetFields = [\n  \"ids\",\n  \"embeddings\",\n  \"metadatas\",\n  \"documents\",\n  \"uris\",\n];\n\n/**\n * Complete record set with required IDs for operations like add/update.\n */\nexport interface RecordSet extends BaseRecordSet {\n  /** Array of unique record identifiers */\n  ids: string[];\n}\n\nexport const recordSetFields = [...baseRecordSetFields, \"ids\"];\n\n/**\n * Record set for query operations with required embeddings.\n */\nexport interface QueryRecordSet extends BaseRecordSet {\n  /** Optional array of record IDs to filter by */\n  ids?: string[];\n  /** Array of query embedding vectors (required for queries) */\n  embeddings: number[][];\n}\n\ntype LiteralValue = string | number | boolean;\n\ntype LogicalOperator = \"$and\" | \"$or\";\n\ntype WhereOperator = \"$gt\" | \"$gte\" | \"$lt\" | \"$lte\" | \"$ne\" | \"$eq\";\n\ntype InclusionExclusionOperator = \"$in\" | \"$nin\";\n\ntype OperatorExpression =\n  | { $gt: LiteralValue }\n  | { $gte: LiteralValue }\n  | { $lt: LiteralValue }\n  | { $lte: LiteralValue }\n  | { $ne: LiteralValue }\n  | { $eq: LiteralValue }\n  | { $and: LiteralValue }\n  | { $or: LiteralValue }\n  | { $in: LiteralValue[] }\n  | { $nin: LiteralValue[] };\n\n/**\n * Where clause for filtering records based on metadata.\n * Supports field equality, comparison operators, and logical operators.\n */\nexport type Where =\n  | { [key: string]: LiteralValue | OperatorExpression }\n  | { $and: Where[] }\n  | { $or: Where[] };\n\ntype WhereDocumentOperator =\n  | \"$contains\"\n  | \"$not_contains\"\n  | \"$matches\"\n  | \"$not_matches\"\n  | \"$regex\"\n  | \"$not_regex\"\n  | LogicalOperator;\n\n/**\n * Where clause for filtering based on document content.\n * Supports text search operators and logical combinations.\n */\nexport type WhereDocument =\n  | { $contains: string }\n  | { $not_contains: string }\n  | { $matches: string }\n  | { $not_matches: string }\n  | { $regex: string }\n  | { $not_regex: string }\n  | { $and: WhereDocument[] }\n  | { $or: WhereDocument[] };\n\n/**\n * Enum specifying which fields to include in query results.\n */\nexport enum IncludeEnum {\n  /** Include similarity distances in results */\n  distances = \"distances\",\n  /** Include document text content in results */\n  documents = \"documents\",\n  /** Include embedding vectors in results */\n  embeddings = \"embeddings\",\n  /** Include metadata objects in results */\n  metadatas = \"metadatas\",\n  /** Include URIs in results */\n  uris = \"uris\",\n}\n\n/**\n * Result class for get operations, containing retrieved records.\n * @template TMeta - The type of metadata associated with records\n */\nexport class GetResult<TMeta extends Metadata = Metadata> {\n  public readonly documents: (string | null)[];\n  public readonly embeddings: number[][];\n  public readonly ids: string[];\n  public readonly include: Include[];\n  public readonly metadatas: (TMeta | null)[];\n  public readonly uris: (string | null)[];\n\n  /**\n   * Creates a new GetResult instance.\n   * @param data - The result data containing all fields\n   */\n  constructor({\n    documents,\n    embeddings,\n    ids,\n    include,\n    metadatas,\n    uris,\n  }: {\n    documents: (string | null)[];\n    embeddings: number[][];\n    ids: string[];\n    include: Include[];\n    metadatas: (TMeta | null)[];\n    uris: (string | null)[];\n  }) {\n    this.documents = documents;\n    this.embeddings = embeddings;\n    this.ids = ids;\n    this.include = include;\n    this.metadatas = metadatas;\n    this.uris = uris;\n  }\n\n  /**\n   * Converts the result to a row-based format for easier iteration.\n   * @returns Object containing include fields and array of record objects\n   */\n  public rows() {\n    return this.ids.map((id, index) => {\n      return {\n        id,\n        document: this.include.includes(\"documents\")\n          ? this.documents[index]\n          : undefined,\n        embedding: this.include.includes(\"embeddings\")\n          ? this.embeddings[index]\n          : undefined,\n        metadata: this.include.includes(\"metadatas\")\n          ? this.metadatas[index]\n          : undefined,\n        uri: this.include.includes(\"uris\") ? this.uris[index] : undefined,\n      };\n    });\n  }\n}\n\n/**\n * Interface for query results in row format.\n * @template TMeta - The type of metadata associated with records\n */\nexport interface QueryRowResult<TMeta extends Metadata = Metadata> {\n  /** Similarity distance to the query (if included) */\n  distance?: number | null;\n  /** Document text content (if included) */\n  document?: string | null;\n  /** Embedding vector (if included) */\n  embedding?: number[] | null;\n  /** Unique record identifier */\n  id: string;\n  /** Record metadata (if included) */\n  metadata?: TMeta | null;\n  /** Record URI (if included) */\n  uri?: string | null;\n}\n\n/**\n * Result class for query operations, containing search results.\n * @template TMeta - The type of metadata associated with records\n */\nexport class QueryResult<TMeta extends Metadata = Metadata> {\n  public readonly distances: (number | null)[][];\n  public readonly documents: (string | null)[][];\n  public readonly embeddings: (number[] | null)[][];\n  public readonly ids: string[][];\n  public readonly include: Include[];\n  public readonly metadatas: (TMeta | null)[][];\n  public readonly uris: (string | null)[][];\n\n  /**\n   * Creates a new QueryResult instance.\n   * @param data - The query result data containing all fields\n   */\n  constructor({\n    distances,\n    documents,\n    embeddings,\n    ids,\n    include,\n    metadatas,\n    uris,\n  }: {\n    distances: (number | null)[][];\n    documents: (string | null)[][];\n    embeddings: (number[] | null)[][];\n    ids: string[][];\n    include: Include[];\n    metadatas: (TMeta | null)[][];\n    uris: (string | null)[][];\n  }) {\n    this.distances = distances;\n    this.documents = documents;\n    this.embeddings = embeddings;\n    this.ids = ids;\n    this.include = include;\n    this.metadatas = metadatas;\n    this.uris = uris;\n  }\n\n  /**\n   * Converts the query result to a row-based format for easier iteration.\n   * @returns Object containing include fields and structured query results\n   */\n  public rows(): QueryRowResult<TMeta>[][] {\n    const queries: {\n      distance?: number | null;\n      document?: string | null;\n      embedding?: number[] | null;\n      id: string;\n      metadata?: TMeta | null;\n      uri?: string | null;\n    }[][] = [];\n\n    for (let q = 0; q < this.ids.length; q++) {\n      const records = this.ids[q].map((id, index) => {\n        return {\n          id,\n          document: this.include.includes(\"documents\")\n            ? this.documents[q][index]\n            : undefined,\n          embedding: this.include.includes(\"embeddings\")\n            ? this.embeddings[q][index]\n            : undefined,\n          metadata: this.include.includes(\"metadatas\")\n            ? this.metadatas[q][index]\n            : undefined,\n          uri: this.include.includes(\"uris\") ? this.uris[q][index] : undefined,\n          distance: this.include.includes(\"distances\")\n            ? this.distances[q][index]\n            : undefined,\n        };\n      });\n\n      queries.push(records);\n    }\n\n    return queries;\n  }\n}\n", "export type AuthToken = string | undefined;\n\nexport interface Auth {\n  /**\n   * Which part of the request do we use to send the auth?\n   *\n   * @default 'header'\n   */\n  in?: 'header' | 'query' | 'cookie';\n  /**\n   * Header or query parameter name.\n   *\n   * @default 'Authorization'\n   */\n  name?: string;\n  scheme?: 'basic' | 'bearer';\n  type: 'apiKey' | 'http';\n}\n\nexport const getAuthToken = async (\n  auth: Auth,\n  callback: ((auth: Auth) => Promise<AuthToken> | AuthToken) | AuthToken,\n): Promise<string | undefined> => {\n  const token =\n    typeof callback === 'function' ? await callback(auth) : callback;\n\n  if (!token) {\n    return;\n  }\n\n  if (auth.scheme === 'bearer') {\n    return `Bearer ${token}`;\n  }\n\n  if (auth.scheme === 'basic') {\n    return `Basic ${btoa(token)}`;\n  }\n\n  return token;\n};\n", "import type {\n  ArrayStyle,\n  ObjectStyle,\n  SerializerOptions,\n} from './pathSerializer';\n\nexport type QuerySerializer = (query: Record<string, unknown>) => string;\n\nexport type BodySerializer = (body: any) => any;\n\nexport interface QuerySerializerOptions {\n  allowReserved?: boolean;\n  array?: SerializerOptions<ArrayStyle>;\n  object?: SerializerOptions<ObjectStyle>;\n}\n\nconst serializeFormDataPair = (data: FormData, key: string, value: unknown) => {\n  if (typeof value === 'string' || value instanceof Blob) {\n    data.append(key, value);\n  } else {\n    data.append(key, JSON.stringify(value));\n  }\n};\n\nconst serializeUrlSearchParamsPair = (\n  data: URLSearchParams,\n  key: string,\n  value: unknown,\n) => {\n  if (typeof value === 'string') {\n    data.append(key, value);\n  } else {\n    data.append(key, JSON.stringify(value));\n  }\n};\n\nexport const formDataBodySerializer = {\n  bodySerializer: <T extends Record<string, any> | Array<Record<string, any>>>(\n    body: T,\n  ) => {\n    const data = new FormData();\n\n    Object.entries(body).forEach(([key, value]) => {\n      if (value === undefined || value === null) {\n        return;\n      }\n      if (Array.isArray(value)) {\n        value.forEach((v) => serializeFormDataPair(data, key, v));\n      } else {\n        serializeFormDataPair(data, key, value);\n      }\n    });\n\n    return data;\n  },\n};\n\nexport const jsonBodySerializer = {\n  bodySerializer: <T>(body: T) =>\n    JSON.stringify(body, (key, value) =>\n      typeof value === 'bigint' ? value.toString() : value,\n    ),\n};\n\nexport const urlSearchParamsBodySerializer = {\n  bodySerializer: <T extends Record<string, any> | Array<Record<string, any>>>(\n    body: T,\n  ) => {\n    const data = new URLSearchParams();\n\n    Object.entries(body).forEach(([key, value]) => {\n      if (value === undefined || value === null) {\n        return;\n      }\n      if (Array.isArray(value)) {\n        value.forEach((v) => serializeUrlSearchParamsPair(data, key, v));\n      } else {\n        serializeUrlSearchParamsPair(data, key, value);\n      }\n    });\n\n    return data.toString();\n  },\n};\n", "interface SerializeOptions<T>\n  extends SerializePrimitiveOptions,\n    SerializerOptions<T> {}\n\ninterface SerializePrimitiveOptions {\n  allowReserved?: boolean;\n  name: string;\n}\n\nexport interface SerializerOptions<T> {\n  /**\n   * @default true\n   */\n  explode: boolean;\n  style: T;\n}\n\nexport type ArrayStyle = 'form' | 'spaceDelimited' | 'pipeDelimited';\nexport type ArraySeparatorStyle = ArrayStyle | MatrixStyle;\ntype MatrixStyle = 'label' | 'matrix' | 'simple';\nexport type ObjectStyle = 'form' | 'deepObject';\ntype ObjectSeparatorStyle = ObjectStyle | MatrixStyle;\n\ninterface SerializePrimitiveParam extends SerializePrimitiveOptions {\n  value: string;\n}\n\nexport const separatorArrayExplode = (style: ArraySeparatorStyle) => {\n  switch (style) {\n    case 'label':\n      return '.';\n    case 'matrix':\n      return ';';\n    case 'simple':\n      return ',';\n    default:\n      return '&';\n  }\n};\n\nexport const separatorArrayNoExplode = (style: ArraySeparatorStyle) => {\n  switch (style) {\n    case 'form':\n      return ',';\n    case 'pipeDelimited':\n      return '|';\n    case 'spaceDelimited':\n      return '%20';\n    default:\n      return ',';\n  }\n};\n\nexport const separatorObjectExplode = (style: ObjectSeparatorStyle) => {\n  switch (style) {\n    case 'label':\n      return '.';\n    case 'matrix':\n      return ';';\n    case 'simple':\n      return ',';\n    default:\n      return '&';\n  }\n};\n\nexport const serializeArrayParam = ({\n  allowReserved,\n  explode,\n  name,\n  style,\n  value,\n}: SerializeOptions<ArraySeparatorStyle> & {\n  value: unknown[];\n}) => {\n  if (!explode) {\n    const joinedValues = (\n      allowReserved ? value : value.map((v) => encodeURIComponent(v as string))\n    ).join(separatorArrayNoExplode(style));\n    switch (style) {\n      case 'label':\n        return `.${joinedValues}`;\n      case 'matrix':\n        return `;${name}=${joinedValues}`;\n      case 'simple':\n        return joinedValues;\n      default:\n        return `${name}=${joinedValues}`;\n    }\n  }\n\n  const separator = separatorArrayExplode(style);\n  const joinedValues = value\n    .map((v) => {\n      if (style === 'label' || style === 'simple') {\n        return allowReserved ? v : encodeURIComponent(v as string);\n      }\n\n      return serializePrimitiveParam({\n        allowReserved,\n        name,\n        value: v as string,\n      });\n    })\n    .join(separator);\n  return style === 'label' || style === 'matrix'\n    ? separator + joinedValues\n    : joinedValues;\n};\n\nexport const serializePrimitiveParam = ({\n  allowReserved,\n  name,\n  value,\n}: SerializePrimitiveParam) => {\n  if (value === undefined || value === null) {\n    return '';\n  }\n\n  if (typeof value === 'object') {\n    throw new Error(\n      'Deeply-nested arrays/objects aren’t supported. Provide your own `querySerializer()` to handle these.',\n    );\n  }\n\n  return `${name}=${allowReserved ? value : encodeURIComponent(value)}`;\n};\n\nexport const serializeObjectParam = ({\n  allowReserved,\n  explode,\n  name,\n  style,\n  value,\n}: SerializeOptions<ObjectSeparatorStyle> & {\n  value: Record<string, unknown> | Date;\n}) => {\n  if (value instanceof Date) {\n    return `${name}=${value.toISOString()}`;\n  }\n\n  if (style !== 'deepObject' && !explode) {\n    let values: string[] = [];\n    Object.entries(value).forEach(([key, v]) => {\n      values = [\n        ...values,\n        key,\n        allowReserved ? (v as string) : encodeURIComponent(v as string),\n      ];\n    });\n    const joinedValues = values.join(',');\n    switch (style) {\n      case 'form':\n        return `${name}=${joinedValues}`;\n      case 'label':\n        return `.${joinedValues}`;\n      case 'matrix':\n        return `;${name}=${joinedValues}`;\n      default:\n        return joinedValues;\n    }\n  }\n\n  const separator = separatorObjectExplode(style);\n  const joinedValues = Object.entries(value)\n    .map(([key, v]) =>\n      serializePrimitiveParam({\n        allowReserved,\n        name: style === 'deepObject' ? `${name}[${key}]` : key,\n        value: v as string,\n      }),\n    )\n    .join(separator);\n  return style === 'label' || style === 'matrix'\n    ? separator + joinedValues\n    : joinedValues;\n};\n", "import type {\n  QuerySerializer,\n  QuerySerializerOptions,\n} from '@hey-api/client-core';\nimport {\n  getAuthToken,\n  jsonBodySerializer,\n  serializeArrayParam,\n  serializeObjectParam,\n  serializePrimitiveParam,\n} from '@hey-api/client-core';\n\nimport type { Client, ClientOptions, Config, RequestOptions } from './types';\n\ninterface PathSerializer {\n  path: Record<string, unknown>;\n  url: string;\n}\n\nconst PATH_PARAM_RE = /\\{[^{}]+\\}/g;\n\ntype ArrayStyle = 'form' | 'spaceDelimited' | 'pipeDelimited';\ntype MatrixStyle = 'label' | 'matrix' | 'simple';\ntype ArraySeparatorStyle = ArrayStyle | MatrixStyle;\n\nconst defaultPathSerializer = ({ path, url: _url }: PathSerializer) => {\n  let url = _url;\n  const matches = _url.match(PATH_PARAM_RE);\n  if (matches) {\n    for (const match of matches) {\n      let explode = false;\n      let name = match.substring(1, match.length - 1);\n      let style: ArraySeparatorStyle = 'simple';\n\n      if (name.endsWith('*')) {\n        explode = true;\n        name = name.substring(0, name.length - 1);\n      }\n\n      if (name.startsWith('.')) {\n        name = name.substring(1);\n        style = 'label';\n      } else if (name.startsWith(';')) {\n        name = name.substring(1);\n        style = 'matrix';\n      }\n\n      const value = path[name];\n\n      if (value === undefined || value === null) {\n        continue;\n      }\n\n      if (Array.isArray(value)) {\n        url = url.replace(\n          match,\n          serializeArrayParam({ explode, name, style, value }),\n        );\n        continue;\n      }\n\n      if (typeof value === 'object') {\n        url = url.replace(\n          match,\n          serializeObjectParam({\n            explode,\n            name,\n            style,\n            value: value as Record<string, unknown>,\n          }),\n        );\n        continue;\n      }\n\n      if (style === 'matrix') {\n        url = url.replace(\n          match,\n          `;${serializePrimitiveParam({\n            name,\n            value: value as string,\n          })}`,\n        );\n        continue;\n      }\n\n      const replaceValue = encodeURIComponent(\n        style === 'label' ? `.${value as string}` : (value as string),\n      );\n      url = url.replace(match, replaceValue);\n    }\n  }\n  return url;\n};\n\nexport const createQuerySerializer = <T = unknown>({\n  allowReserved,\n  array,\n  object,\n}: QuerySerializerOptions = {}) => {\n  const querySerializer = (queryParams: T) => {\n    let search: string[] = [];\n    if (queryParams && typeof queryParams === 'object') {\n      for (const name in queryParams) {\n        const value = queryParams[name];\n\n        if (value === undefined || value === null) {\n          continue;\n        }\n\n        if (Array.isArray(value)) {\n          search = [\n            ...search,\n            serializeArrayParam({\n              allowReserved,\n              explode: true,\n              name,\n              style: 'form',\n              value,\n              ...array,\n            }),\n          ];\n          continue;\n        }\n\n        if (typeof value === 'object') {\n          search = [\n            ...search,\n            serializeObjectParam({\n              allowReserved,\n              explode: true,\n              name,\n              style: 'deepObject',\n              value: value as Record<string, unknown>,\n              ...object,\n            }),\n          ];\n          continue;\n        }\n\n        search = [\n          ...search,\n          serializePrimitiveParam({\n            allowReserved,\n            name,\n            value: value as string,\n          }),\n        ];\n      }\n    }\n    return search.join('&');\n  };\n  return querySerializer;\n};\n\n/**\n * Infers parseAs value from provided Content-Type header.\n */\nexport const getParseAs = (\n  contentType: string | null,\n): Exclude<Config['parseAs'], 'auto'> => {\n  if (!contentType) {\n    // If no Content-Type header is provided, the best we can do is return the raw response body,\n    // which is effectively the same as the 'stream' option.\n    return 'stream';\n  }\n\n  const cleanContent = contentType.split(';')[0]?.trim();\n\n  if (!cleanContent) {\n    return;\n  }\n\n  if (\n    cleanContent.startsWith('application/json') ||\n    cleanContent.endsWith('+json')\n  ) {\n    return 'json';\n  }\n\n  if (cleanContent === 'multipart/form-data') {\n    return 'formData';\n  }\n\n  if (\n    ['application/', 'audio/', 'image/', 'video/'].some((type) =>\n      cleanContent.startsWith(type),\n    )\n  ) {\n    return 'blob';\n  }\n\n  if (cleanContent.startsWith('text/')) {\n    return 'text';\n  }\n};\n\nexport const setAuthParams = async ({\n  security,\n  ...options\n}: Pick<Required<RequestOptions>, 'security'> &\n  Pick<RequestOptions, 'auth' | 'query'> & {\n    headers: Headers;\n  }) => {\n  for (const auth of security) {\n    const token = await getAuthToken(auth, options.auth);\n\n    if (!token) {\n      continue;\n    }\n\n    const name = auth.name ?? 'Authorization';\n\n    switch (auth.in) {\n      case 'query':\n        if (!options.query) {\n          options.query = {};\n        }\n        options.query[name] = token;\n        break;\n      case 'cookie':\n        options.headers.append('Cookie', `${name}=${token}`);\n        break;\n      case 'header':\n      default:\n        options.headers.set(name, token);\n        break;\n    }\n\n    return;\n  }\n};\n\nexport const buildUrl: Client['buildUrl'] = (options) => {\n  const url = getUrl({\n    baseUrl: options.baseUrl as string,\n    path: options.path,\n    query: options.query,\n    querySerializer:\n      typeof options.querySerializer === 'function'\n        ? options.querySerializer\n        : createQuerySerializer(options.querySerializer),\n    url: options.url,\n  });\n  return url;\n};\n\nexport const getUrl = ({\n  baseUrl,\n  path,\n  query,\n  querySerializer,\n  url: _url,\n}: {\n  baseUrl?: string;\n  path?: Record<string, unknown>;\n  query?: Record<string, unknown>;\n  querySerializer: QuerySerializer;\n  url: string;\n}) => {\n  const pathUrl = _url.startsWith('/') ? _url : `/${_url}`;\n  let url = (baseUrl ?? '') + pathUrl;\n  if (path) {\n    url = defaultPathSerializer({ path, url });\n  }\n  let search = query ? querySerializer(query) : '';\n  if (search.startsWith('?')) {\n    search = search.substring(1);\n  }\n  if (search) {\n    url += `?${search}`;\n  }\n  return url;\n};\n\nexport const mergeConfigs = (a: Config, b: Config): Config => {\n  const config = { ...a, ...b };\n  if (config.baseUrl?.endsWith('/')) {\n    config.baseUrl = config.baseUrl.substring(0, config.baseUrl.length - 1);\n  }\n  config.headers = mergeHeaders(a.headers, b.headers);\n  return config;\n};\n\nexport const mergeHeaders = (\n  ...headers: Array<Required<Config>['headers'] | undefined>\n): Headers => {\n  const mergedHeaders = new Headers();\n  for (const header of headers) {\n    if (!header || typeof header !== 'object') {\n      continue;\n    }\n\n    const iterator =\n      header instanceof Headers ? header.entries() : Object.entries(header);\n\n    for (const [key, value] of iterator) {\n      if (value === null) {\n        mergedHeaders.delete(key);\n      } else if (Array.isArray(value)) {\n        for (const v of value) {\n          mergedHeaders.append(key, v as string);\n        }\n      } else if (value !== undefined) {\n        // assume object headers are meant to be JSON stringified, i.e. their\n        // content value in OpenAPI specification is 'application/json'\n        mergedHeaders.set(\n          key,\n          typeof value === 'object' ? JSON.stringify(value) : (value as string),\n        );\n      }\n    }\n  }\n  return mergedHeaders;\n};\n\ntype ErrInterceptor<Err, Res, Req, Options> = (\n  error: Err,\n  response: Res,\n  request: Req,\n  options: Options,\n) => Err | Promise<Err>;\n\ntype ReqInterceptor<Req, Options> = (\n  request: Req,\n  options: Options,\n) => Req | Promise<Req>;\n\ntype ResInterceptor<Res, Req, Options> = (\n  response: Res,\n  request: Req,\n  options: Options,\n) => Res | Promise<Res>;\n\nclass Interceptors<Interceptor> {\n  _fns: Interceptor[];\n\n  constructor() {\n    this._fns = [];\n  }\n\n  clear() {\n    this._fns = [];\n  }\n\n  exists(fn: Interceptor) {\n    return this._fns.indexOf(fn) !== -1;\n  }\n\n  eject(fn: Interceptor) {\n    const index = this._fns.indexOf(fn);\n    if (index !== -1) {\n      this._fns = [...this._fns.slice(0, index), ...this._fns.slice(index + 1)];\n    }\n  }\n\n  use(fn: Interceptor) {\n    this._fns = [...this._fns, fn];\n  }\n}\n\n// `createInterceptors()` response, meant for external use as it does not\n// expose internals\nexport interface Middleware<Req, Res, Err, Options> {\n  error: Pick<\n    Interceptors<ErrInterceptor<Err, Res, Req, Options>>,\n    'eject' | 'use'\n  >;\n  request: Pick<Interceptors<ReqInterceptor<Req, Options>>, 'eject' | 'use'>;\n  response: Pick<\n    Interceptors<ResInterceptor<Res, Req, Options>>,\n    'eject' | 'use'\n  >;\n}\n\n// do not add `Middleware` as return type so we can use _fns internally\nexport const createInterceptors = <Req, Res, Err, Options>() => ({\n  error: new Interceptors<ErrInterceptor<Err, Res, Req, Options>>(),\n  request: new Interceptors<ReqInterceptor<Req, Options>>(),\n  response: new Interceptors<ResInterceptor<Res, Req, Options>>(),\n});\n\nconst defaultQuerySerializer = createQuerySerializer({\n  allowReserved: false,\n  array: {\n    explode: true,\n    style: 'form',\n  },\n  object: {\n    explode: true,\n    style: 'deepObject',\n  },\n});\n\nconst defaultHeaders = {\n  'Content-Type': 'application/json',\n};\n\nexport const createConfig = <T extends ClientOptions = ClientOptions>(\n  override: Config<Omit<ClientOptions, keyof T> & T> = {},\n): Config<Omit<ClientOptions, keyof T> & T> => ({\n  ...jsonBodySerializer,\n  headers: defaultHeaders,\n  parseAs: 'auto',\n  querySerializer: defaultQuerySerializer,\n  ...override,\n});\n", "import type { Client, Config, RequestOptions } from './types';\nimport {\n  buildUrl,\n  createConfig,\n  createInterceptors,\n  getParseAs,\n  mergeConfigs,\n  mergeHeaders,\n  setAuthParams,\n} from './utils';\n\ntype ReqInit = Omit<RequestInit, 'body' | 'headers'> & {\n  body?: any;\n  headers: ReturnType<typeof mergeHeaders>;\n};\n\nexport const createClient = (config: Config = {}): Client => {\n  let _config = mergeConfigs(createConfig(), config);\n\n  const getConfig = (): Config => ({ ..._config });\n\n  const setConfig = (config: Config): Config => {\n    _config = mergeConfigs(_config, config);\n    return getConfig();\n  };\n\n  const interceptors = createInterceptors<\n    Request,\n    Response,\n    unknown,\n    RequestOptions\n  >();\n\n  // @ts-expect-error\n  const request: Client['request'] = async (options) => {\n    const opts = {\n      ..._config,\n      ...options,\n      fetch: options.fetch ?? _config.fetch ?? globalThis.fetch,\n      headers: mergeHeaders(_config.headers, options.headers),\n    };\n\n    if (opts.security) {\n      await setAuthParams({\n        ...opts,\n        security: opts.security,\n      });\n    }\n\n    if (opts.body && opts.bodySerializer) {\n      opts.body = opts.bodySerializer(opts.body);\n    }\n\n    // remove Content-Type header if body is empty to avoid sending invalid requests\n    if (opts.body === undefined || opts.body === '') {\n      opts.headers.delete('Content-Type');\n    }\n\n    const url = buildUrl(opts);\n    const requestInit: ReqInit = {\n      redirect: 'follow',\n      ...opts,\n    };\n\n    let request = new Request(url, requestInit);\n\n    for (const fn of interceptors.request._fns) {\n      request = await fn(request, opts);\n    }\n\n    // fetch must be assigned here, otherwise it would throw the error:\n    // TypeError: Failed to execute 'fetch' on 'Window': Illegal invocation\n    const _fetch = opts.fetch!;\n    let response = await _fetch(request);\n\n    for (const fn of interceptors.response._fns) {\n      response = await fn(response, request, opts);\n    }\n\n    const result = {\n      request,\n      response,\n    };\n\n    if (response.ok) {\n      if (\n        response.status === 204 ||\n        response.headers.get('Content-Length') === '0'\n      ) {\n        return {\n          data: {},\n          ...result,\n        };\n      }\n\n      const parseAs =\n        (opts.parseAs === 'auto'\n          ? getParseAs(response.headers.get('Content-Type'))\n          : opts.parseAs) ?? 'json';\n\n      if (parseAs === 'stream') {\n        return {\n          data: response.body,\n          ...result,\n        };\n      }\n\n      let data = await response[parseAs]();\n      if (parseAs === 'json') {\n        if (opts.responseValidator) {\n          await opts.responseValidator(data);\n        }\n\n        if (opts.responseTransformer) {\n          data = await opts.responseTransformer(data);\n        }\n      }\n\n      return {\n        data,\n        ...result,\n      };\n    }\n\n    let error = await response.text();\n\n    try {\n      error = JSON.parse(error);\n    } catch {\n      // noop\n    }\n\n    let finalError = error;\n\n    for (const fn of interceptors.error._fns) {\n      finalError = (await fn(error, response, request, opts)) as string;\n    }\n\n    finalError = finalError || ({} as string);\n\n    if (opts.throwOnError) {\n      throw finalError;\n    }\n\n    return {\n      error: finalError,\n      ...result,\n    };\n  };\n\n  return {\n    buildUrl,\n    connect: (options) => request({ ...options, method: 'CONNECT' }),\n    delete: (options) => request({ ...options, method: 'DELETE' }),\n    get: (options) => request({ ...options, method: 'GET' }),\n    getConfig,\n    head: (options) => request({ ...options, method: 'HEAD' }),\n    interceptors,\n    options: (options) => request({ ...options, method: 'OPTIONS' }),\n    patch: (options) => request({ ...options, method: 'PATCH' }),\n    post: (options) => request({ ...options, method: 'POST' }),\n    put: (options) => request({ ...options, method: 'PUT' }),\n    request,\n    setConfig,\n    trace: (options) => request({ ...options, method: 'TRACE' }),\n  };\n};\n", "// This file is auto-generated by @hey-api/openapi-ts\n\nimport type { ClientOptions } from './types.gen';\nimport { type Config, type ClientOptions as DefaultClientOptions, createClient, createConfig } from '@hey-api/client-fetch';\n\n/**\n * The `createClientConfig()` function will be called on client initialization\n * and the returned object will become the client's initial configuration.\n *\n * You may want to initialize your client this way instead of calling\n * `setConfig()`. This is useful for example if you're using Next.js\n * to ensure your client always has the correct values.\n */\nexport type CreateClientConfig<T extends DefaultClientOptions = ClientOptions> = (override?: Config<DefaultClientOptions & T>) => Config<Required<DefaultClientOptions> & T>;\n\nexport const client = createClient(createConfig<ClientOptions>({\n    baseUrl: 'http://localhost:8000',\n    throwOnError: true\n}));", "// This file is auto-generated by @hey-api/openapi-ts\n\nimport type { Options as ClientOptions, TDataShape, Client } from '@hey-api/client-fetch';\nimport type { GetUserIdentityData, GetUserIdentityResponse2, GetUserIdentityError, HealthcheckData, HealthcheckResponse, HealthcheckError, HeartbeatData, HeartbeatResponse2, HeartbeatError, PreFlightChecksData, PreFlightChecksResponse, PreFlightChecksError, ResetData, ResetResponse, ResetError, CreateTenantData, CreateTenantResponse2, CreateTenantError, GetTenantData, GetTenantResponse2, GetTenantError, ListDatabasesData, ListDatabasesResponse, ListDatabasesError, CreateDatabaseData, CreateDatabaseResponse2, CreateDatabaseError, DeleteDatabaseData, DeleteDatabaseResponse2, DeleteDatabaseError, GetDatabaseData, GetDatabaseResponse, GetDatabaseError, ListCollectionsData, ListCollectionsResponse, ListCollectionsError, CreateCollectionData, CreateCollectionResponse, CreateCollectionError, DeleteCollectionData, DeleteCollectionResponse, DeleteCollectionError, GetCollectionData, GetCollectionResponse, GetCollectionError, UpdateCollectionData, UpdateCollectionResponse2, UpdateCollectionError, CollectionAddData, CollectionAddResponse, CollectionCountData, CollectionCountResponse, CollectionCountError, CollectionDeleteData, CollectionDeleteResponse, CollectionDeleteError, ForkCollectionData, ForkCollectionResponse, ForkCollectionError, CollectionGetData, CollectionGetResponse, CollectionGetError, CollectionQueryData, CollectionQueryResponse, CollectionQueryError, CollectionUpdateData, CollectionUpdateResponse, CollectionUpsertData, CollectionUpsertResponse, CollectionUpsertError, CountCollectionsData, CountCollectionsResponse, CountCollectionsError, VersionData, VersionResponse } from './types.gen';\nimport { client as _heyApiClient } from './client.gen';\n\nexport type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {\n    /**\n     * You can provide a client instance returned by `createClient()` instead of\n     * individual options. This might be also useful if you want to implement a\n     * custom client.\n     */\n    client?: Client;\n    /**\n     * You can pass arbitrary values through the `meta` object. This can be\n     * used to access values that aren't defined as part of the SDK function.\n     */\n    meta?: Record<string, unknown>;\n};\n\nexport class DefaultService {\n    /**\n     * Retrieves the current user's identity, tenant, and databases.\n     */\n    public static getUserIdentity<ThrowOnError extends boolean = true>(options?: Options<GetUserIdentityData, ThrowOnError>) {\n        return (options?.client ?? _heyApiClient).get<GetUserIdentityResponse2, GetUserIdentityError, ThrowOnError>({\n            url: '/api/v2/auth/identity',\n            ...options\n        });\n    }\n    \n    /**\n     * Health check endpoint that returns 200 if the server and executor are ready\n     */\n    public static healthcheck<ThrowOnError extends boolean = true>(options?: Options<HealthcheckData, ThrowOnError>) {\n        return (options?.client ?? _heyApiClient).get<HealthcheckResponse, HealthcheckError, ThrowOnError>({\n            url: '/api/v2/healthcheck',\n            ...options\n        });\n    }\n    \n    /**\n     * Heartbeat endpoint that returns a nanosecond timestamp of the current time.\n     */\n    public static heartbeat<ThrowOnError extends boolean = true>(options?: Options<HeartbeatData, ThrowOnError>) {\n        return (options?.client ?? _heyApiClient).get<HeartbeatResponse2, HeartbeatError, ThrowOnError>({\n            url: '/api/v2/heartbeat',\n            ...options\n        });\n    }\n    \n    /**\n     * Pre-flight checks endpoint reporting basic readiness info.\n     */\n    public static preFlightChecks<ThrowOnError extends boolean = true>(options?: Options<PreFlightChecksData, ThrowOnError>) {\n        return (options?.client ?? _heyApiClient).get<PreFlightChecksResponse, PreFlightChecksError, ThrowOnError>({\n            url: '/api/v2/pre-flight-checks',\n            ...options\n        });\n    }\n    \n    /**\n     * Reset endpoint allowing authorized users to reset the database.\n     */\n    public static reset<ThrowOnError extends boolean = true>(options?: Options<ResetData, ThrowOnError>) {\n        return (options?.client ?? _heyApiClient).post<ResetResponse, ResetError, ThrowOnError>({\n            url: '/api/v2/reset',\n            ...options\n        });\n    }\n    \n    /**\n     * Creates a new tenant.\n     */\n    public static createTenant<ThrowOnError extends boolean = true>(options: Options<CreateTenantData, ThrowOnError>) {\n        return (options.client ?? _heyApiClient).post<CreateTenantResponse2, CreateTenantError, ThrowOnError>({\n            url: '/api/v2/tenants',\n            ...options,\n            headers: {\n                'Content-Type': 'application/json',\n                ...options?.headers\n            }\n        });\n    }\n    \n    /**\n     * Returns an existing tenant by name.\n     */\n    public static getTenant<ThrowOnError extends boolean = true>(options: Options<GetTenantData, ThrowOnError>) {\n        return (options.client ?? _heyApiClient).get<GetTenantResponse2, GetTenantError, ThrowOnError>({\n            url: '/api/v2/tenants/{tenant_name}',\n            ...options\n        });\n    }\n    \n    /**\n     * Lists all databases for a given tenant.\n     */\n    public static listDatabases<ThrowOnError extends boolean = true>(options: Options<ListDatabasesData, ThrowOnError>) {\n        return (options.client ?? _heyApiClient).get<ListDatabasesResponse, ListDatabasesError, ThrowOnError>({\n            url: '/api/v2/tenants/{tenant}/databases',\n            ...options\n        });\n    }\n    \n    /**\n     * Creates a new database for a given tenant.\n     */\n    public static createDatabase<ThrowOnError extends boolean = true>(options: Options<CreateDatabaseData, ThrowOnError>) {\n        return (options.client ?? _heyApiClient).post<CreateDatabaseResponse2, CreateDatabaseError, ThrowOnError>({\n            url: '/api/v2/tenants/{tenant}/databases',\n            ...options,\n            headers: {\n                'Content-Type': 'application/json',\n                ...options?.headers\n            }\n        });\n    }\n    \n    /**\n     * Deletes a specific database.\n     */\n    public static deleteDatabase<ThrowOnError extends boolean = true>(options: Options<DeleteDatabaseData, ThrowOnError>) {\n        return (options.client ?? _heyApiClient).delete<DeleteDatabaseResponse2, DeleteDatabaseError, ThrowOnError>({\n            url: '/api/v2/tenants/{tenant}/databases/{database}',\n            ...options\n        });\n    }\n    \n    /**\n     * Retrieves a specific database by name.\n     */\n    public static getDatabase<ThrowOnError extends boolean = true>(options: Options<GetDatabaseData, ThrowOnError>) {\n        return (options.client ?? _heyApiClient).get<GetDatabaseResponse, GetDatabaseError, ThrowOnError>({\n            url: '/api/v2/tenants/{tenant}/databases/{database}',\n            ...options\n        });\n    }\n    \n    /**\n     * Lists all collections in the specified database.\n     */\n    public static listCollections<ThrowOnError extends boolean = true>(options: Options<ListCollectionsData, ThrowOnError>) {\n        return (options.client ?? _heyApiClient).get<ListCollectionsResponse, ListCollectionsError, ThrowOnError>({\n            url: '/api/v2/tenants/{tenant}/databases/{database}/collections',\n            ...options\n        });\n    }\n    \n    /**\n     * Creates a new collection under the specified database.\n     */\n    public static createCollection<ThrowOnError extends boolean = true>(options: Options<CreateCollectionData, ThrowOnError>) {\n        return (options.client ?? _heyApiClient).post<CreateCollectionResponse, CreateCollectionError, ThrowOnError>({\n            url: '/api/v2/tenants/{tenant}/databases/{database}/collections',\n            ...options,\n            headers: {\n                'Content-Type': 'application/json',\n                ...options?.headers\n            }\n        });\n    }\n    \n    /**\n     * Deletes a collection in a given database.\n     */\n    public static deleteCollection<ThrowOnError extends boolean = true>(options: Options<DeleteCollectionData, ThrowOnError>) {\n        return (options.client ?? _heyApiClient).delete<DeleteCollectionResponse, DeleteCollectionError, ThrowOnError>({\n            url: '/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}',\n            ...options\n        });\n    }\n    \n    /**\n     * Retrieves a collection by ID or name.\n     */\n    public static getCollection<ThrowOnError extends boolean = true>(options: Options<GetCollectionData, ThrowOnError>) {\n        return (options.client ?? _heyApiClient).get<GetCollectionResponse, GetCollectionError, ThrowOnError>({\n            url: '/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}',\n            ...options\n        });\n    }\n    \n    /**\n     * Updates an existing collection's name or metadata.\n     */\n    public static updateCollection<ThrowOnError extends boolean = true>(options: Options<UpdateCollectionData, ThrowOnError>) {\n        return (options.client ?? _heyApiClient).put<UpdateCollectionResponse2, UpdateCollectionError, ThrowOnError>({\n            url: '/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}',\n            ...options,\n            headers: {\n                'Content-Type': 'application/json',\n                ...options?.headers\n            }\n        });\n    }\n    \n    /**\n     * Adds records to a collection.\n     */\n    public static collectionAdd<ThrowOnError extends boolean = true>(options: Options<CollectionAddData, ThrowOnError>) {\n        return (options.client ?? _heyApiClient).post<CollectionAddResponse, unknown, ThrowOnError>({\n            url: '/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/add',\n            ...options,\n            headers: {\n                'Content-Type': 'application/json',\n                ...options?.headers\n            }\n        });\n    }\n    \n    /**\n     * Retrieves the number of records in a collection.\n     */\n    public static collectionCount<ThrowOnError extends boolean = true>(options: Options<CollectionCountData, ThrowOnError>) {\n        return (options.client ?? _heyApiClient).get<CollectionCountResponse, CollectionCountError, ThrowOnError>({\n            url: '/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/count',\n            ...options\n        });\n    }\n    \n    /**\n     * Deletes records in a collection. Can filter by IDs or metadata.\n     */\n    public static collectionDelete<ThrowOnError extends boolean = true>(options: Options<CollectionDeleteData, ThrowOnError>) {\n        return (options.client ?? _heyApiClient).post<CollectionDeleteResponse, CollectionDeleteError, ThrowOnError>({\n            url: '/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/delete',\n            ...options,\n            headers: {\n                'Content-Type': 'application/json',\n                ...options?.headers\n            }\n        });\n    }\n    \n    /**\n     * Forks an existing collection.\n     */\n    public static forkCollection<ThrowOnError extends boolean = true>(options: Options<ForkCollectionData, ThrowOnError>) {\n        return (options.client ?? _heyApiClient).post<ForkCollectionResponse, ForkCollectionError, ThrowOnError>({\n            url: '/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/fork',\n            ...options,\n            headers: {\n                'Content-Type': 'application/json',\n                ...options?.headers\n            }\n        });\n    }\n    \n    /**\n     * Retrieves records from a collection by ID or metadata filter.\n     */\n    public static collectionGet<ThrowOnError extends boolean = true>(options: Options<CollectionGetData, ThrowOnError>) {\n        return (options.client ?? _heyApiClient).post<CollectionGetResponse, CollectionGetError, ThrowOnError>({\n            url: '/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/get',\n            ...options,\n            headers: {\n                'Content-Type': 'application/json',\n                ...options?.headers\n            }\n        });\n    }\n    \n    /**\n     * Query a collection in a variety of ways, including vector search, metadata filtering, and full-text search\n     */\n    public static collectionQuery<ThrowOnError extends boolean = true>(options: Options<CollectionQueryData, ThrowOnError>) {\n        return (options.client ?? _heyApiClient).post<CollectionQueryResponse, CollectionQueryError, ThrowOnError>({\n            url: '/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/query',\n            ...options,\n            headers: {\n                'Content-Type': 'application/json',\n                ...options?.headers\n            }\n        });\n    }\n    \n    /**\n     * Updates records in a collection by ID.\n     */\n    public static collectionUpdate<ThrowOnError extends boolean = true>(options: Options<CollectionUpdateData, ThrowOnError>) {\n        return (options.client ?? _heyApiClient).post<CollectionUpdateResponse, unknown, ThrowOnError>({\n            url: '/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/update',\n            ...options,\n            headers: {\n                'Content-Type': 'application/json',\n                ...options?.headers\n            }\n        });\n    }\n    \n    /**\n     * Upserts records in a collection (create if not exists, otherwise update).\n     */\n    public static collectionUpsert<ThrowOnError extends boolean = true>(options: Options<CollectionUpsertData, ThrowOnError>) {\n        return (options.client ?? _heyApiClient).post<CollectionUpsertResponse, CollectionUpsertError, ThrowOnError>({\n            url: '/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/upsert',\n            ...options,\n            headers: {\n                'Content-Type': 'application/json',\n                ...options?.headers\n            }\n        });\n    }\n    \n    /**\n     * Retrieves the total number of collections in a given database.\n     */\n    public static countCollections<ThrowOnError extends boolean = true>(options: Options<CountCollectionsData, ThrowOnError>) {\n        return (options.client ?? _heyApiClient).get<CountCollectionsResponse, CountCollectionsError, ThrowOnError>({\n            url: '/api/v2/tenants/{tenant}/databases/{database}/collections_count',\n            ...options\n        });\n    }\n    \n    /**\n     * Returns the version of the server.\n     */\n    public static version<ThrowOnError extends boolean = true>(options?: Options<VersionData, ThrowOnError>) {\n        return (options?.client ?? _heyApiClient).get<VersionResponse, unknown, ThrowOnError>({\n            url: '/api/v2/version',\n            ...options\n        });\n    }\n    \n}", "/**\n * This is a generic Chroma error.\n */\nexport class ChromaError extends Error {\n  constructor(name: string, message: string, public readonly cause?: unknown) {\n    super(message);\n    this.name = name;\n  }\n}\n\n/**\n * Indicates that there was a problem with the connection to the Chroma server (e.g. the server is down or the client is not connected to the internet)\n */\nexport class ChromaConnectionError extends Error {\n  name = \"ChromaConnectionError\";\n  constructor(message: string, public readonly cause?: unknown) {\n    super(message);\n  }\n}\n\n/** Indicates that the server encountered an error while handling the request. */\nexport class ChromaServerError extends Error {\n  name = \"ChromaServerError\";\n  constructor(message: string, public readonly cause?: unknown) {\n    super(message);\n  }\n}\n\n/** Indicate that there was an issue with the request that the client made. */\nexport class ChromaClientError extends Error {\n  name = \"ChromaClientError\";\n  constructor(message: string, public readonly cause?: unknown) {\n    super(message);\n  }\n}\n\n/** The request lacked valid authentication. */\nexport class ChromaUnauthorizedError extends Error {\n  name = \"ChromaAuthError\";\n  constructor(message: string, public readonly cause?: unknown) {\n    super(message);\n  }\n}\n\n/** The user does not have permission to access the requested resource. */\nexport class ChromaForbiddenError extends Error {\n  name = \"ChromaForbiddenError\";\n  constructor(message: string, public readonly cause?: unknown) {\n    super(message);\n  }\n}\n\nexport class ChromaNotFoundError extends Error {\n  name = \"ChromaNotFoundError\";\n  constructor(message: string, public readonly cause?: unknown) {\n    super(message);\n  }\n}\n\nexport class ChromaValueError extends Error {\n  name = \"ChromaValueError\";\n  constructor(message: string, public readonly cause?: unknown) {\n    super(message);\n  }\n}\n\nexport class InvalidCollectionError extends Error {\n  name = \"InvalidCollectionError\";\n  constructor(message: string, public readonly cause?: unknown) {\n    super(message);\n  }\n}\n\nexport class InvalidArgumentError extends Error {\n  name = \"InvalidArgumentError\";\n  constructor(message: string, public readonly cause?: unknown) {\n    super(message);\n  }\n}\n\nexport class ChromaUniqueError extends Error {\n  name = \"ChromaUniqueError\";\n  constructor(message: string, public readonly cause?: unknown) {\n    super(message);\n  }\n}\n\nexport class ChromaQuotaExceededError extends Error {\n  name = \"ChromaQuotaExceededError\";\n  constructor(message: string, public readonly cause?: unknown) {\n    super(message);\n  }\n}\n\nexport function createErrorByType(type: string, message: string) {\n  switch (type) {\n    case \"InvalidCollection\":\n      return new InvalidCollectionError(message);\n    case \"InvalidArgumentError\":\n      return new InvalidArgumentError(message);\n    default:\n      return undefined;\n  }\n}\n", "import { AdminClientArgs } from \"./admin-client\";\nimport { ChromaClientArgs } from \"./chroma-client\";\nimport {\n  BaseRecordSet,\n  IncludeEnum,\n  Metadata,\n  RecordSet,\n  recordSetFields,\n  Where,\n  WhereDocument,\n} from \"./types\";\nimport { Include } from \"./api\";\nimport { ChromaValueError } from \"./errors\";\n\n/** Default tenant name used when none is specified */\nexport const DEFAULT_TENANT = \"default_tenant\";\n/** Default database name used when none is specified */\nexport const DEFAULT_DATABASE = \"default_database\";\n\n/** Default configuration for AdminClient connections */\nexport const defaultAdminClientArgs: AdminClientArgs = {\n  host: \"localhost\",\n  port: 8000,\n  ssl: false,\n};\n\n/** Default configuration for ChromaClient connections */\nexport const defaultChromaClientArgs: ChromaClientArgs = {\n  ...defaultAdminClientArgs,\n  tenant: DEFAULT_TENANT,\n  database: DEFAULT_DATABASE,\n};\n\n/**\n * Supported HTTP methods for API requests.\n */\nexport type HttpMethod =\n  | \"GET\"\n  | \"POST\"\n  | \"PUT\"\n  | \"DELETE\"\n  | \"HEAD\"\n  | \"CONNECT\"\n  | \"OPTIONS\"\n  | \"PATCH\"\n  | \"TRACE\"\n  | undefined;\n\n/**\n * Normalizes HTTP method strings to standard uppercase format.\n * @param method - HTTP method string to normalize\n * @returns Normalized HttpMethod or undefined if invalid\n */\nexport const normalizeMethod = (method?: string): HttpMethod => {\n  if (method) {\n    switch (method.toUpperCase()) {\n      case \"GET\":\n        return \"GET\";\n      case \"POST\":\n        return \"POST\";\n      case \"PUT\":\n        return \"PUT\";\n      case \"DELETE\":\n        return \"DELETE\";\n      case \"HEAD\":\n        return \"HEAD\";\n      case \"CONNECT\":\n        return \"CONNECT\";\n      case \"OPTIONS\":\n        return \"OPTIONS\";\n      case \"PATCH\":\n        return \"PATCH\";\n      case \"TRACE\":\n        return \"TRACE\";\n      default:\n        return undefined;\n    }\n  }\n  return undefined;\n};\n\n/**\n * Validates that all arrays in a RecordSet have consistent lengths.\n * @param recordSet - The record set to validate\n * @throws ChromaValueError if arrays have inconsistent lengths or are empty\n */\nexport const validateRecordSetLengthConsistency = (recordSet: RecordSet) => {\n  const lengths: [string, number][] = Object.entries(recordSet)\n    .filter(\n      ([field, value]) =>\n        recordSetFields.includes(field) && value !== undefined,\n    )\n    .map(([field, value]) => [field, value.length]);\n\n  if (lengths.length === 0) {\n    throw new ChromaValueError(\n      `At least one of ${recordSetFields.join(\", \")} must be provided`,\n    );\n  }\n\n  const zeroLength = lengths\n    .filter(([_, length]) => length === 0)\n    .map(([field, _]) => field);\n  if (zeroLength.length > 0) {\n    throw new ChromaValueError(\n      `Non-empty lists are required for ${zeroLength.join(\", \")}`,\n    );\n  }\n\n  if (new Set(lengths.map(([_, length]) => length)).size > 1) {\n    throw new ChromaValueError(\n      `Unequal lengths for fields ${lengths\n        .map(([field, _]) => field)\n        .join(\", \")}`,\n    );\n  }\n};\n\nconst validateEmbeddings = ({\n  embeddings,\n  fieldName = \"embeddings\",\n}: {\n  embeddings: number[][];\n  fieldName: string;\n}) => {\n  if (!Array.isArray(embeddings)) {\n    throw new ChromaValueError(\n      `Expected '${fieldName}' to be an array, but got ${typeof embeddings}`,\n    );\n  }\n\n  if (embeddings.length === 0) {\n    throw new ChromaValueError(\n      \"Expected embeddings to be an array with at least one item\",\n    );\n  }\n\n  if (!embeddings.filter((e) => e.every((n: any) => typeof n === \"number\"))) {\n    throw new ChromaValueError(\n      \"Expected each embedding to be an array of numbers\",\n    );\n  }\n\n  embeddings.forEach((embedding, i) => {\n    if (embedding.length === 0) {\n      throw new ChromaValueError(\n        `Expected each embedding to be a non-empty array of numbers, but got an empty array at index ${i}`,\n      );\n    }\n  });\n};\n\nconst validateDocuments = ({\n  documents,\n  nullable = false,\n  fieldName = \"documents\",\n}: {\n  documents: (string | null | undefined)[];\n  fieldName: string;\n  nullable?: boolean;\n}) => {\n  if (!Array.isArray(documents)) {\n    throw new ChromaValueError(\n      `Expected '${fieldName}' to be an array, but got ${typeof documents}`,\n    );\n  }\n\n  if (documents.length === 0) {\n    throw new ChromaValueError(\n      `Expected '${fieldName}' to be a non-empty list`,\n    );\n  }\n\n  documents.forEach((document) => {\n    if (!nullable && typeof document !== \"string\" && !document) {\n      throw new ChromaValueError(\n        `Expected each document to be a string, but got ${typeof document}`,\n      );\n    }\n  });\n};\n\n/**\n * Validates an array of IDs for type correctness and uniqueness.\n * @param ids - Array of ID strings to validate\n * @throws ChromaValueError if IDs are not strings, empty, or contain duplicates\n */\nexport const validateIDs = (ids: string[]) => {\n  if (!Array.isArray(ids)) {\n    throw new ChromaValueError(\n      `Expected 'ids' to be an array, but got ${typeof ids}`,\n    );\n  }\n\n  if (ids.length === 0) {\n    throw new ChromaValueError(\"Expected 'ids' to be a non-empty list\");\n  }\n\n  const nonStrings = ids\n    .map((id, i) => [id, i] as [any, number])\n    .filter(([id, _]) => typeof id !== \"string\")\n    .map(([_, i]) => i);\n\n  if (nonStrings.length > 0) {\n    throw new ChromaValueError(\n      `Found non-string IDs at ${nonStrings.join(\", \")}`,\n    );\n  }\n\n  const seen = new Set();\n  const duplicates = ids.filter((id) => {\n    if (seen.has(id)) {\n      return id;\n    }\n    seen.add(id);\n  });\n  let message = \"Expected IDs to be unique, but found duplicates of\";\n  if (duplicates.length > 0 && duplicates.length <= 5) {\n    throw new ChromaValueError(`${message} ${duplicates.join(\", \")}`);\n  }\n  if (duplicates.length > 0) {\n    throw new ChromaValueError(\n      `${message} ${duplicates.slice(0, 5).join(\", \")}, ..., ${duplicates\n        .slice(duplicates.length - 5)\n        .join(\", \")}`,\n    );\n  }\n};\n\n/**\n * Validates metadata object for correct types and non-emptiness.\n * @param metadata - Metadata object to validate\n * @throws ChromaValueError if metadata is invalid\n */\nexport const validateMetadata = (metadata?: Metadata) => {\n  if (!metadata) {\n    return;\n  }\n\n  if (Object.keys(metadata).length === 0) {\n    throw new ChromaValueError(\"Expected metadata to be non-empty\");\n  }\n\n  if (\n    !Object.values(metadata).every(\n      (v: any) =>\n        v === null ||\n        v === undefined ||\n        typeof v === \"string\" ||\n        typeof v === \"number\" ||\n        typeof v === \"boolean\",\n    )\n  ) {\n    throw new ChromaValueError(\n      \"Expected metadata to be a string, number, boolean, or nullable\",\n    );\n  }\n};\n\nconst validateMetadatas = (metadatas: Metadata[]) => {\n  if (!Array.isArray(metadatas)) {\n    throw new ChromaValueError(\n      `Expected metadatas to be an array, but got ${typeof metadatas}`,\n    );\n  }\n\n  metadatas.forEach((metadata) => validateMetadata(metadata));\n};\n\n/**\n * Validates a base record set for required fields and data consistency.\n * @param options - Validation options\n * @param options.recordSet - The record set to validate\n * @param options.update - Whether this is for an update operation (relaxes requirements)\n * @param options.embeddingsField - Name of the embeddings field for error messages\n * @param options.documentsField - Name of the documents field for error messages\n * @throws ChromaValueError if validation fails\n */\nexport const validateBaseRecordSet = ({\n  recordSet,\n  update = false,\n  embeddingsField = \"embeddings\",\n  documentsField = \"documents\",\n}: {\n  recordSet: BaseRecordSet;\n  update?: boolean;\n  embeddingsField?: string;\n  documentsField?: string;\n}) => {\n  if (!recordSet.embeddings && !recordSet.documents && !update) {\n    throw new ChromaValueError(\n      `At least one of '${embeddingsField}' and '${documentsField}' must be provided`,\n    );\n  }\n\n  if (recordSet.embeddings) {\n    validateEmbeddings({\n      embeddings: recordSet.embeddings,\n      fieldName: embeddingsField,\n    });\n  }\n\n  if (recordSet.documents) {\n    validateDocuments({\n      documents: recordSet.documents,\n      fieldName: documentsField,\n    });\n  }\n\n  if (recordSet.metadatas) {\n    validateMetadatas(recordSet.metadatas);\n  }\n};\n\nexport const validateMaxBatchSize = (recordSetLength: number, maxBatchSize: number) => {\n  if (recordSetLength > maxBatchSize) {\n    throw new ChromaValueError(`Record set length ${recordSetLength} exceeds max batch size ${maxBatchSize}`);\n  }\n};\n\n/**\n * Validates a where clause for metadata filtering.\n * @param where - Where clause object to validate\n * @throws ChromaValueError if the where clause is malformed\n */\nexport const validateWhere = (where: Where) => {\n  if (typeof where !== \"object\") {\n    throw new ChromaValueError(\"Expected where to be a non-empty object\");\n  }\n\n  if (Object.keys(where).length != 1) {\n    throw new ChromaValueError(\n      `Expected 'where' to have exactly one operator, but got ${Object.keys(where).length\n      }`,\n    );\n  }\n\n  Object.entries(where).forEach(([key, value]) => {\n    if (\n      key !== \"$and\" &&\n      key !== \"$or\" &&\n      key !== \"$in\" &&\n      key !== \"$nin\" &&\n      ![\"string\", \"number\", \"boolean\", \"object\"].includes(typeof value)\n    ) {\n      throw new ChromaValueError(\n        `Expected 'where' value to be a string, number, boolean, or an operator expression, but got ${value}`,\n      );\n    }\n\n    if (key === \"$and\" || key === \"$or\") {\n      if (Object.keys(value).length <= 1) {\n        throw new ChromaValueError(\n          `Expected 'where' value for $and or $or to be a list of 'where' expressions, but got ${value}`,\n        );\n      }\n\n      value.forEach((w: Where) => validateWhere(w));\n      return;\n    }\n\n    if (typeof value === \"object\") {\n      if (Object.keys(value).length != 1) {\n        throw new ChromaValueError(\n          `Expected operator expression to have one operator, but got ${value}`,\n        );\n      }\n\n      const [operator, operand] = Object.entries(value)[0];\n\n      if (\n        [\"$gt\", \"$gte\", \"$lt\", \"$lte\"].includes(operator) &&\n        typeof operand !== \"number\"\n      ) {\n        throw new ChromaValueError(\n          `Expected operand value to be a number for ${operator}, but got ${typeof operand}`,\n        );\n      }\n\n      if ([\"$in\", \"$nin\"].includes(operator) && !Array.isArray(operand)) {\n        throw new ChromaValueError(\n          `Expected operand value to be an array for ${operator}, but got ${operand}`,\n        );\n      }\n\n      if (\n        ![\"$gt\", \"$gte\", \"$lt\", \"$lte\", \"$ne\", \"$eq\", \"$in\", \"$nin\"].includes(\n          operator,\n        )\n      ) {\n        throw new ChromaValueError(\n          `Expected operator to be one of $gt, $gte, $lt, $lte, $ne, $eq, $in, $nin, but got ${operator}`,\n        );\n      }\n\n      if (\n        ![\"string\", \"number\", \"boolean\"].includes(typeof operand) &&\n        !Array.isArray(operand)\n      ) {\n        throw new ChromaValueError(\n          \"Expected operand value to be a string, number, boolean, or a list of those types\",\n        );\n      }\n\n      if (\n        Array.isArray(operand) &&\n        (operand.length === 0 ||\n          !operand.every((item) => typeof item === typeof operand[0]))\n      ) {\n        throw new ChromaValueError(\n          \"Expected 'where' operand value to be a non-empty list and all values to be of the same type\",\n        );\n      }\n    }\n  });\n};\n\n/**\n * Validates a where document clause for document content filtering.\n * @param whereDocument - Where document clause to validate\n * @throws ChromaValueError if the clause is malformed\n */\nexport const validateWhereDocument = (whereDocument: WhereDocument) => {\n  if (typeof whereDocument !== \"object\") {\n    throw new ChromaValueError(\n      \"Expected 'whereDocument' to be a non-empty object\",\n    );\n  }\n\n  if (Object.keys(whereDocument).length != 1) {\n    throw new ChromaValueError(\n      `Expected 'whereDocument' to have exactly one operator, but got ${whereDocument}`,\n    );\n  }\n\n  const [operator, operand] = Object.entries(whereDocument)[0];\n  if (\n    ![\n      \"$contains\",\n      \"$not_contains\",\n      \"$matches\",\n      \"$not_matches\",\n      \"$regex\",\n      \"$not_regex\",\n      \"$and\",\n      \"$or\",\n    ].includes(operator)\n  ) {\n    throw new ChromaValueError(\n      `Expected 'whereDocument' operator to be one of $contains, $not_contains, $matches, $not_matches, $regex, $not_regex, $and, or $or, but got ${operator}`,\n    );\n  }\n\n  if (operator === \"$and\" || operator === \"$or\") {\n    if (!Array.isArray(operand)) {\n      throw new ChromaValueError(\n        `Expected operand for ${operator} to be a list of 'whereDocument' expressions, but got ${operand}`,\n      );\n    }\n\n    if (operand.length <= 1) {\n      throw new ChromaValueError(\n        `Expected 'whereDocument' operand for ${operator} to be a list with at least two 'whereDocument' expressions`,\n      );\n    }\n\n    operand.forEach((item) => validateWhereDocument(item));\n  }\n\n  if (\n    (operand === \"$contains\" ||\n      operand === \"$not_contains\" ||\n      operand === \"$regex\" ||\n      operand === \"$not_regex\") &&\n    (typeof (operator as any) !== \"string\" || operator.length === 0)\n  ) {\n    throw new ChromaValueError(\n      `Expected operand for ${operator} to be a non empty string, but got ${operand}`,\n    );\n  }\n};\n\n/**\n * Validates include fields for query operations.\n * @param options - Validation options\n * @param options.include - Array of fields to include in results\n * @param options.exclude - Optional array of fields that should not be included\n * @throws ChromaValueError if include fields are invalid\n */\nexport const validateInclude = ({\n  include,\n  exclude,\n}: {\n  include: Include[];\n  exclude?: Include[];\n}) => {\n  if (!Array.isArray(include)) {\n    throw new ChromaValueError(\"Expected 'include' to be a non-empty array\");\n  }\n\n  const validValues = Object.keys(IncludeEnum);\n  include.forEach((item) => {\n    if (typeof (item as any) !== \"string\") {\n      throw new ChromaValueError(\"Expected 'include' items to be strings\");\n    }\n\n    if (!validValues.includes(item)) {\n      throw new ChromaValueError(\n        `Expected 'include' items to be one of ${validValues.join(\n          \", \",\n        )}, but got ${item}`,\n      );\n    }\n\n    if (exclude?.includes(item)) {\n      throw new ChromaValueError(`${item} is not allowed for this operation`);\n    }\n  });\n};\n\n/**\n * Validates the number of results parameter for queries.\n * @param nResults - Number of results to validate\n * @throws ChromaValueError if nResults is not a positive number\n */\nexport const validateNResults = (nResults: number) => {\n  if (typeof (nResults as any) !== \"number\") {\n    throw new ChromaValueError(\n      `Expected 'nResults' to be a number, but got ${typeof nResults}`,\n    );\n  }\n\n  if (nResults <= 0) {\n    throw new ChromaValueError(\"Number of requested results has to positive\");\n  }\n};\n\nexport const parseConnectionPath = (path: string) => {\n  try {\n    const url = new URL(path);\n\n    const ssl = url.protocol === \"https:\";\n    const host = url.hostname;\n    const port = url.port;\n\n    return {\n      ssl,\n      host,\n      port: Number(port),\n    };\n  } catch {\n    throw new ChromaValueError(`Invalid URL: ${path}`);\n  }\n};\nconst packEmbedding = (embedding: number[]): ArrayBuffer => {\n  const buffer = new ArrayBuffer(embedding.length * 4);\n  const view = new Float32Array(buffer);\n  for (let i = 0; i < embedding.length; i++) {\n    view[i] = embedding[i];\n  }\n  return buffer;\n};\n\nexport const optionalEmbeddingsToBase64Bytes = (embeddings: number[][] | undefined) => {\n  if (!embeddings) {\n    return undefined;\n  }\n\n  return embeddings.map(embedding => {\n    const buffer = packEmbedding(embedding);\n\n    const uint8Array = new Uint8Array(buffer);\n    const binaryString = Array.from(uint8Array, byte => String.fromCharCode(byte)).join('');\n    return btoa(binaryString);\n  });\n};\n", "import { EmbeddingFunctionConfiguration } from \"./api\";\nimport { ChromaValueError } from \"./errors\";\n\n/**\n * Supported vector space types.\n */\nexport type EmbeddingFunctionSpace = \"cosine\" | \"l2\" | \"ip\";\n\n/**\n * Interface for embedding functions.\n * Embedding functions transform text documents into numerical representations\n * that can be used for similarity search and other vector operations.\n */\nexport interface EmbeddingFunction {\n  /**\n   * Generates embeddings for the given texts.\n   * @param texts - Array of text strings to embed\n   * @returns Promise resolving to array of embedding vectors\n   */\n  generate(texts: string[]): Promise<number[][]>;\n  /** Optional name identifier for the embedding function */\n  name?: string;\n  /** Returns the default vector space for this embedding function */\n  defaultSpace?(): EmbeddingFunctionSpace;\n  /** Returns all supported vector spaces for this embedding function */\n  supportedSpaces?(): EmbeddingFunctionSpace[];\n  /** Creates an instance from configuration object */\n  buildFromConfig?(config: Record<string, any>): EmbeddingFunction;\n  /** Returns the current configuration as an object */\n  getConfig?(): Record<string, any>;\n  /**\n   * Validates that a configuration update is allowed.\n   * @param newConfig - New configuration to validate\n   */\n  validateConfigUpdate?(newConfig: Record<string, any>): void;\n  /**\n   * Validates that a configuration object is valid.\n   * @param config - Configuration to validate\n   */\n  validateConfig?(config: Record<string, any>): void;\n}\n\n/**\n * Interface for embedding function constructor classes.\n * Used for registering and instantiating embedding functions.\n */\nexport interface EmbeddingFunctionClass {\n  /** Constructor for creating new instances */\n  new (...args: any[]): EmbeddingFunction;\n  /** Name identifier for the embedding function */\n  name: string;\n  /** Static method to build instance from configuration */\n  buildFromConfig(config: Record<string, any>): EmbeddingFunction;\n}\n\n/**\n * Registry of available embedding functions.\n * Maps function names to their constructor classes.\n */\nexport const knownEmbeddingFunctions = new Map<\n  string,\n  EmbeddingFunctionClass\n>();\n\n/**\n * Registers an embedding function in the global registry.\n * @param name - Unique name for the embedding function\n * @param fn - Embedding function class to register\n * @throws ChromaValueError if name is already registered\n */\nexport const registerEmbeddingFunction = (\n  name: string,\n  fn: EmbeddingFunctionClass,\n) => {\n  if (knownEmbeddingFunctions.has(name)) {\n    throw new ChromaValueError(\n      `Embedding function with name ${name} is already registered.`,\n    );\n  }\n  knownEmbeddingFunctions.set(name, fn);\n};\n\n/**\n * Retrieves and instantiates an embedding function from configuration.\n * @param collectionName - Name of the collection (for error messages)\n * @param efConfig - Configuration for the embedding function\n * @returns Promise resolving to an EmbeddingFunction instance\n */\nexport const getEmbeddingFunction = async (\n  collectionName: string,\n  efConfig?: EmbeddingFunctionConfiguration,\n) => {\n  if (!efConfig) {\n    console.warn(\n      `No embedding function configuration found for collection ${collectionName}. 'add' and 'query' will fail unless you provide them embeddings directly.`,\n    );\n    return undefined;\n  }\n\n  if (efConfig.type === \"legacy\") {\n    console.warn(\n      `No embedding function configuration found for collection ${collectionName}. 'add' and 'query' will fail unless you provide them embeddings directly.`,\n    );\n    return undefined;\n  }\n\n  const name = efConfig.name;\n\n  const embeddingFunction = knownEmbeddingFunctions.get(name);\n  if (!embeddingFunction) {\n    console.warn(\n      `Collection ${collectionName} was created with the ${embeddingFunction} embedding function. However, the @chroma-core/${embeddingFunction} package is not install. 'add' and 'query' will fail unless you provide them embeddings directly, or install the @chroma-core/${embeddingFunction} package.`,\n    );\n    return undefined;\n  }\n\n  let constructorConfig: Record<string, any> =\n    efConfig.type === \"known\" ? (efConfig.config as Record<string, any>) : {};\n\n  try {\n    if (embeddingFunction.buildFromConfig) {\n      return embeddingFunction.buildFromConfig(constructorConfig);\n    }\n\n    console.warn(\n      `Embedding function ${name} does not define a 'buildFromConfig' function. 'add' and 'query' will fail unless you provide them embeddings directly.`,\n    );\n    return undefined;\n  } catch (e) {\n    console.warn(\n      `Embedding function ${name} failed to build with config: ${constructorConfig}. 'add' and 'query' will fail unless you provide them embeddings directly. Error: ${e}`,\n    );\n    return undefined;\n  }\n};\n\n/**\n * Serializes an embedding function to configuration format.\n * @param embeddingFunction - User provided embedding function\n * @param configEmbeddingFunction - Collection config embedding function\n * @returns Configuration object that can recreate the function\n */\nexport const serializeEmbeddingFunction = ({\n  embeddingFunction,\n  configEmbeddingFunction,\n}: {\n  embeddingFunction?: EmbeddingFunction;\n  configEmbeddingFunction?: EmbeddingFunction;\n}): EmbeddingFunctionConfiguration | undefined => {\n  if (embeddingFunction && configEmbeddingFunction) {\n    throw new ChromaValueError(\n      \"Embedding function provided when already defined in the collection configuration\",\n    );\n  }\n\n  if (!embeddingFunction && !configEmbeddingFunction) {\n    return undefined;\n  }\n\n  const ef = embeddingFunction || configEmbeddingFunction!;\n  if (\n    !ef.getConfig ||\n    !ef.name ||\n    !(ef.constructor as EmbeddingFunctionClass).buildFromConfig\n  ) {\n    return { type: \"legacy\" };\n  }\n\n  if (ef.validateConfig) ef.validateConfig(ef.getConfig());\n  return {\n    name: ef.name,\n    type: \"known\",\n    config: ef.getConfig(),\n  };\n};\n\n/**\n * Gets the configuration for the default embedding function.\n * Dynamically imports and registers the default embedding function if needed.\n * @returns Promise resolving to default embedding function configuration\n * @throws Error if default embedding function cannot be loaded\n */\nexport const getDefaultEFConfig =\n  async (): Promise<EmbeddingFunctionConfiguration> => {\n    try {\n      const { DefaultEmbeddingFunction } = await import(\n        \"@chroma-core/default-embed\"\n      );\n      if (!knownEmbeddingFunctions.has(new DefaultEmbeddingFunction().name)) {\n        registerEmbeddingFunction(\"default\", DefaultEmbeddingFunction);\n      }\n    } catch (e) {\n      console.error(e);\n      throw new Error(\n        \"Cannot instantiate a collection with the DefaultEmbeddingFunction. Please install @chroma-core/default-embed, or provide a different embedding function\",\n      );\n    }\n    return {\n      name: \"default\",\n      type: \"known\",\n      config: {},\n    };\n  };\n", "import { ChromaValueError } from \"./errors\";\nimport {\n  EmbeddingFunctionConfiguration,\n  HnswConfiguration as ApiHnswConfiguration,\n  SpannConfiguration,\n  UpdateCollectionConfiguration as ApiUpdateCollectionConfiguration,\n} from \"./api\";\nimport {\n  EmbeddingFunction,\n  getDefaultEFConfig,\n  getEmbeddingFunction,\n  serializeEmbeddingFunction,\n} from \"./embedding-function\";\n\nexport interface CollectionConfiguration {\n  embeddingFunction?: EmbeddingFunctionConfiguration | null;\n  hnsw?: HNSWConfiguration | null;\n  spann?: SpannConfiguration | null;\n}\n\nexport type HNSWConfiguration = ApiHnswConfiguration & {\n  batch_size?: number | null;\n  num_threads?: number | null;\n};\n\nexport type CreateCollectionConfiguration = Omit<\n  CollectionConfiguration,\n  \"embeddingFunction\"\n> & { embeddingFunction?: EmbeddingFunction };\n\nexport interface UpdateCollectionConfiguration {\n  embeddingFunction?: EmbeddingFunction;\n  hnsw?: UpdateHNSWConfiguration;\n  spann?: UpdateSPANNConfiguration;\n}\n\nexport interface UpdateHNSWConfiguration {\n  batch_size?: number;\n  ef_search?: number;\n  num_threads?: number;\n  resize_factor?: number;\n  sync_threshold?: number;\n}\n\nexport interface UpdateSPANNConfiguration {\n  search_nprobe?: number;\n  ef_search?: number;\n}\n\n/**\n * Validate user provided collection configuration and embedding function. Returns a\n * CollectionConfiguration to be used in collection creation.\n */\nexport const processCreateCollectionConfig = async ({\n  configuration,\n  embeddingFunction,\n}: {\n  configuration?: CreateCollectionConfiguration;\n  embeddingFunction?: EmbeddingFunction;\n}) => {\n  if (configuration?.hnsw && configuration?.spann) {\n    throw new ChromaValueError(\n      \"Cannot specify both HNSW and SPANN configurations\",\n    );\n  }\n\n  const embeddingFunctionConfiguration =\n    serializeEmbeddingFunction({\n      embeddingFunction,\n      configEmbeddingFunction: configuration?.embeddingFunction,\n    }) || (await getDefaultEFConfig());\n\n  return {\n    ...(configuration || {}),\n    embedding_function: embeddingFunctionConfiguration,\n  } as CollectionConfiguration;\n};\n\n/**\n *\n */\nexport const processUpdateCollectionConfig = async ({\n  collectionName,\n  currentConfiguration,\n  currentEmbeddingFunction,\n  newConfiguration,\n}: {\n  collectionName: string;\n  currentConfiguration: CollectionConfiguration;\n  currentEmbeddingFunction?: EmbeddingFunction;\n  newConfiguration: UpdateCollectionConfiguration;\n}): Promise<{\n  updateConfiguration?: ApiUpdateCollectionConfiguration;\n  updateEmbeddingFunction?: EmbeddingFunction;\n}> => {\n  if (newConfiguration.hnsw && typeof newConfiguration.hnsw !== \"object\") {\n    throw new ChromaValueError(\n      \"Invalid HNSW config provided in UpdateCollectionConfiguration\",\n    );\n  }\n\n  if (newConfiguration.spann && typeof newConfiguration.spann !== \"object\") {\n    throw new ChromaValueError(\n      \"Invalid SPANN config provided in UpdateCollectionConfiguration\",\n    );\n  }\n\n  const embeddingFunction =\n    currentEmbeddingFunction ||\n    (await getEmbeddingFunction(\n      collectionName,\n      currentConfiguration.embeddingFunction ?? undefined,\n    ));\n\n  const newEmbeddingFunction = newConfiguration.embeddingFunction;\n\n  if (\n    embeddingFunction &&\n    embeddingFunction.validateConfigUpdate &&\n    newEmbeddingFunction &&\n    newEmbeddingFunction.getConfig\n  ) {\n    embeddingFunction.validateConfigUpdate(newEmbeddingFunction.getConfig());\n  }\n\n  return {\n    updateConfiguration: {\n      hnsw: newConfiguration.hnsw,\n      spann: newConfiguration.spann,\n      embedding_function:\n        newEmbeddingFunction &&\n        serializeEmbeddingFunction({ embeddingFunction: newEmbeddingFunction }),\n    },\n    updateEmbeddingFunction: newEmbeddingFunction,\n  };\n};\n", "import { ChromaClient } from \"./chroma-client\";\nimport { EmbeddingFunction } from \"./embedding-function\";\nimport {\n  BaseRecordSet,\n  CollectionMetadata,\n  GetResult,\n  Metadata,\n  QueryRecordSet,\n  QueryResult,\n  RecordSet,\n  Where,\n  WhereDocument,\n} from \"./types\";\nimport { Include } from \"./api\";\nimport { DefaultService as Api } from \"./api\";\nimport {\n  validateRecordSetLengthConsistency,\n  validateIDs,\n  validateInclude,\n  validateBaseRecordSet,\n  validateWhere,\n  validateWhereDocument,\n  validateNResults,\n  validateMetadata,\n  optionalEmbeddingsToBase64Bytes,\n  validateMaxBatchSize,\n} from \"./utils\";\nimport { createClient } from \"@hey-api/client-fetch\";\nimport { ChromaValueError } from \"./errors\";\nimport {\n  CollectionConfiguration,\n  processUpdateCollectionConfig,\n  UpdateCollectionConfiguration,\n} from \"./collection-configuration\";\n\n/**\n * Interface for collection operations using collection ID.\n * Provides methods for adding, querying, updating, and deleting records.\n */\nexport interface Collection {\n  /** Unique identifier for the collection */\n  id: string;\n  /** Name of the collection */\n  name: string;\n  /** Collection-level metadata */\n  metadata: CollectionMetadata | undefined;\n  /** Collection configuration settings */\n  configuration: CollectionConfiguration;\n  /** Optional embedding function. Must match the one used to create the collection. */\n  embeddingFunction?: EmbeddingFunction;\n  /** Gets the total number of records in the collection */\n  count(): Promise<number>;\n  /**\n   * Adds new records to the collection.\n   * @param args - Record data to add\n   */\n  add(args: {\n    /** Unique identifiers for the records */\n    ids: string[];\n    /** Optional pre-computed embeddings */\n    embeddings?: number[][];\n    /** Optional metadata for each record */\n    metadatas?: Metadata[];\n    /** Optional document text (will be embedded if embeddings not provided) */\n    documents?: string[];\n    /** Optional URIs for the records */\n    uris?: string[];\n  }): Promise<void>;\n  /**\n   * Retrieves records from the collection based on filters.\n   * @template TMeta - Type of metadata for type safety\n   * @param args - Query parameters for filtering records\n   * @returns Promise resolving to matching records\n   */\n  get<TMeta extends Metadata = Metadata>(args?: {\n    /** Specific record IDs to retrieve */\n    ids?: string[];\n    /** Metadata-based filtering conditions */\n    where?: Where;\n    /** Maximum number of records to return */\n    limit?: number;\n    /** Number of records to skip */\n    offset?: number;\n    /** Document content-based filtering conditions */\n    whereDocument?: WhereDocument;\n    /** Fields to include in the response */\n    include?: Include[];\n  }): Promise<GetResult<TMeta>>;\n  /**\n   * Retrieves a preview of records from the collection.\n   * @param args - Preview options\n   * @returns Promise resolving to a sample of records\n   */\n  peek(args: { limit?: number }): Promise<GetResult>;\n  /**\n   * Performs similarity search on the collection.\n   * @template TMeta - Type of metadata for type safety\n   * @param args - Query parameters for similarity search\n   * @returns Promise resolving to similar records ranked by distance\n   */\n  query<TMeta extends Metadata = Metadata>(args: {\n    /** Pre-computed query embedding vectors */\n    queryEmbeddings?: number[][];\n    /** Query text to be embedded and searched */\n    queryTexts?: string[];\n    /** Query URIs to be processed */\n    queryURIs?: string[];\n    /** Filter to specific record IDs */\n    ids?: string[];\n    /** Maximum number of results per query (default: 10) */\n    nResults?: number;\n    /** Metadata-based filtering conditions */\n    where?: Where;\n    /** Full-text search conditions */\n    whereDocument?: WhereDocument;\n    /** Fields to include in the response */\n    include?: Include[];\n  }): Promise<QueryResult<TMeta>>;\n  /**\n   * Modifies collection properties like name, metadata, or configuration.\n   * @param args - Properties to update\n   */\n  modify(args: {\n    /** New name for the collection */\n    name?: string;\n    /** New metadata for the collection */\n    metadata?: CollectionMetadata;\n    /** New configuration settings */\n    configuration?: UpdateCollectionConfiguration;\n  }): Promise<void>;\n  /**\n   * Creates a copy of the collection with a new name.\n   * @param args - Fork options\n   * @returns Promise resolving to the new Collection instance\n   */\n  fork({ name }: { name: string }): Promise<Collection>;\n  /**\n   * Updates existing records in the collection.\n   * @param args - Record data to update\n   */\n  update(args: {\n    /** IDs of records to update */\n    ids: string[];\n    /** New embedding vectors */\n    embeddings?: number[][];\n    /** New metadata */\n    metadatas?: Metadata[];\n    /** New document text */\n    documents?: string[];\n    /** New URIs */\n    uris?: string[];\n  }): Promise<void>;\n  /**\n   * Inserts new records or updates existing ones (upsert operation).\n   * @param args - Record data to upsert\n   */\n  upsert(args: {\n    /** IDs of records to upsert */\n    ids: string[];\n    /** Embedding vectors */\n    embeddings?: number[][];\n    /** Metadata */\n    metadatas?: Metadata[];\n    /** Document text */\n    documents?: string[];\n    /** URIs */\n    uris?: string[];\n  }): Promise<void>;\n  /**\n   * Deletes records from the collection based on filters.\n   * @param args - Deletion criteria\n   */\n  delete(args: {\n    /** Specific record IDs to delete */\n    ids?: string[];\n    /** Metadata-based filtering for deletion */\n    where?: Where;\n    /** Document content-based filtering for deletion */\n    whereDocument?: WhereDocument;\n  }): Promise<void>;\n}\n\n/**\n * Arguments for creating a Collection instance.\n */\nexport interface CollectionArgs {\n  /** ChromaDB client instance */\n  chromaClient: ChromaClient;\n  /** HTTP API client */\n  apiClient: ReturnType<typeof createClient>;\n  /** Collection name */\n  name: string;\n  /** Collection ID */\n  id: string;\n  /** Embedding function for the collection */\n  embeddingFunction?: EmbeddingFunction;\n  /** Collection configuration */\n  configuration: CollectionConfiguration;\n  /** Optional collection metadata */\n  metadata?: CollectionMetadata;\n}\n\n/**\n * Implementation of CollectionAPI for ID-based collection operations.\n * Provides core functionality for interacting with collections using their ID.\n */\nexport class CollectionImpl implements Collection {\n  protected readonly chromaClient: ChromaClient;\n  protected readonly apiClient: ReturnType<typeof createClient>;\n  public readonly id: string;\n  private _name: string;\n  private _metadata: CollectionMetadata | undefined;\n  private _configuration: CollectionConfiguration;\n  protected _embeddingFunction: EmbeddingFunction | undefined;\n\n  /**\n   * Creates a new CollectionAPIImpl instance.\n   * @param options - Configuration for the collection API\n   */\n  constructor({\n    chromaClient,\n    apiClient,\n    id,\n    name,\n    metadata,\n    configuration,\n    embeddingFunction,\n  }: CollectionArgs) {\n    this.chromaClient = chromaClient;\n    this.apiClient = apiClient;\n    this.id = id;\n    this._name = name;\n    this._metadata = metadata;\n    this._configuration = configuration;\n    this._embeddingFunction = embeddingFunction;\n  }\n\n  public get name(): string {\n    return this._name;\n  }\n\n  private set name(name: string) {\n    this._name = name;\n  }\n\n  public get configuration(): CollectionConfiguration {\n    return this._configuration;\n  }\n\n  private set configuration(configuration: CollectionConfiguration) {\n    this._configuration = configuration;\n  }\n\n  public get metadata(): CollectionMetadata | undefined {\n    return this._metadata;\n  }\n\n  private set metadata(metadata: CollectionMetadata | undefined) {\n    this._metadata = metadata;\n  }\n\n  public get embeddingFunction(): EmbeddingFunction | undefined {\n    return this._embeddingFunction;\n  }\n\n  protected set embeddingFunction(\n    embeddingFunction: EmbeddingFunction | undefined,\n  ) {\n    this._embeddingFunction = embeddingFunction;\n  }\n\n  protected async path(): Promise<{\n    tenant: string;\n    database: string;\n    collection_id: string;\n  }> {\n    const clientPath = await this.chromaClient._path();\n    return {\n      ...clientPath,\n      collection_id: this.id,\n    };\n  }\n\n  private async embed(documents: string[]): Promise<number[][]> {\n    if (!this._embeddingFunction) {\n      throw new ChromaValueError(\n        \"Embedding function must be defined for operations requiring embeddings.\",\n      );\n    }\n\n    return await this._embeddingFunction.generate(documents);\n  }\n\n  private async prepareRecords({\n    recordSet,\n    maxBatchSize,\n    update = false,\n  }: {\n    recordSet: RecordSet;\n    maxBatchSize: number;\n    update?: boolean;\n  }) {\n    validateRecordSetLengthConsistency(recordSet);\n    validateIDs(recordSet.ids);\n    validateBaseRecordSet({ recordSet, update });\n    validateMaxBatchSize(recordSet.ids.length, maxBatchSize);\n\n    if (!recordSet.embeddings && recordSet.documents) {\n      recordSet.embeddings = await this.embed(recordSet.documents);\n    }\n  }\n\n  private validateGet(\n    include: Include[],\n    ids?: string[],\n    where?: Where,\n    whereDocument?: WhereDocument,\n  ) {\n    validateInclude({ include, exclude: [\"distances\"] });\n    if (ids) validateIDs(ids);\n    if (where) validateWhere(where);\n    if (whereDocument) validateWhereDocument(whereDocument);\n  }\n\n  private async prepareQuery(\n    recordSet: BaseRecordSet,\n    include: Include[],\n    ids?: string[],\n    where?: Where,\n    whereDocument?: WhereDocument,\n    nResults?: number,\n  ): Promise<QueryRecordSet> {\n    validateBaseRecordSet({\n      recordSet,\n      embeddingsField: \"queryEmbeddings\",\n      documentsField: \"queryTexts\",\n    });\n    validateInclude({ include });\n\n    if (ids) validateIDs(ids);\n    if (where) validateWhere(where);\n    if (whereDocument) validateWhereDocument(whereDocument);\n    if (nResults) validateNResults(nResults);\n\n    let embeddings: number[][];\n    if (!recordSet.embeddings) {\n      embeddings = await this.embed(recordSet.documents!);\n    } else {\n      embeddings = recordSet.embeddings;\n    }\n\n    return {\n      ...recordSet,\n      ids,\n      embeddings,\n    };\n  }\n\n  private validateDelete(\n    ids?: string[],\n    where?: Where,\n    whereDocument?: WhereDocument,\n  ) {\n    if (ids) validateIDs(ids);\n    if (where) validateWhere(where);\n    if (whereDocument) validateWhereDocument(whereDocument);\n  }\n\n  public async count(): Promise<number> {\n    const { data } = await Api.collectionCount({\n      client: this.apiClient,\n      path: await this.path(),\n    });\n\n    return data;\n  }\n\n  public async add({\n    ids,\n    embeddings,\n    metadatas,\n    documents,\n    uris,\n  }: {\n    ids: string[];\n    embeddings?: number[][];\n    metadatas?: Metadata[];\n    documents?: string[];\n    uris?: string[];\n  }) {\n    const recordSet: RecordSet = {\n      ids,\n      embeddings,\n      documents,\n      metadatas,\n      uris,\n    };\n\n    const maxBatchSize = await this.chromaClient.getMaxBatchSize();\n\n    await this.prepareRecords({ recordSet, maxBatchSize });\n\n    const supportsBase64Encoding = await this.chromaClient.supportsBase64Encoding();\n    const embeddingsBase64 = supportsBase64Encoding ? optionalEmbeddingsToBase64Bytes(recordSet.embeddings) : recordSet.embeddings;\n\n\n    await Api.collectionAdd({\n      client: this.apiClient,\n      path: await this.path(),\n      body: {\n        ids: recordSet.ids,\n        embeddings: embeddingsBase64,\n        documents: recordSet.documents,\n        metadatas: recordSet.metadatas,\n        uris: recordSet.uris,\n      },\n    });\n  }\n\n  public async get<TMeta extends Metadata = Metadata>(\n    args: Partial<{\n      ids?: string[];\n      where?: Where;\n      limit?: number;\n      offset?: number;\n      whereDocument?: WhereDocument;\n      include?: Include[];\n    }> = {},\n  ): Promise<GetResult<TMeta>> {\n    const {\n      ids,\n      where,\n      limit,\n      offset,\n      whereDocument,\n      include = [\"documents\", \"metadatas\"],\n    } = args;\n\n    this.validateGet(include, ids, where, whereDocument);\n\n    const { data } = await Api.collectionGet({\n      client: this.apiClient,\n      path: await this.path(),\n      body: {\n        ids,\n        where,\n        limit,\n        offset,\n        where_document: whereDocument,\n        include,\n      },\n    });\n\n    return new GetResult<TMeta>({\n      documents: data.documents ?? [],\n      embeddings: data.embeddings ?? [],\n      ids: data.ids,\n      include: data.include,\n      metadatas: (data.metadatas ?? []) as (TMeta | null)[],\n      uris: data.uris ?? [],\n    });\n  }\n\n  public async peek({ limit = 10 }: { limit?: number }): Promise<GetResult> {\n    return this.get({ limit });\n  }\n\n  public async query<TMeta extends Metadata = Metadata>({\n    queryEmbeddings,\n    queryTexts,\n    queryURIs,\n    ids,\n    nResults = 10,\n    where,\n    whereDocument,\n    include = [\"metadatas\", \"documents\", \"distances\"],\n  }: {\n    queryEmbeddings?: number[][];\n    queryTexts?: string[];\n    queryURIs?: string[];\n    ids?: string[];\n    nResults?: number;\n    where?: Where;\n    whereDocument?: WhereDocument;\n    include?: Include[];\n  }): Promise<QueryResult<TMeta>> {\n    const recordSet: BaseRecordSet = {\n      embeddings: queryEmbeddings,\n      documents: queryTexts,\n      uris: queryURIs,\n    };\n\n    const queryRecordSet = await this.prepareQuery(\n      recordSet,\n      include,\n      ids,\n      where,\n      whereDocument,\n      nResults,\n    );\n\n    const { data } = await Api.collectionQuery({\n      client: this.apiClient,\n      path: await this.path(),\n      body: {\n        ids: queryRecordSet.ids,\n        include,\n        n_results: nResults,\n        query_embeddings: queryRecordSet.embeddings,\n        where,\n        where_document: whereDocument,\n      },\n    });\n\n    return new QueryResult({\n      distances: data.distances ?? [],\n      documents: data.documents ?? [],\n      embeddings: data.embeddings ?? [],\n      ids: data.ids ?? [],\n      include: data.include,\n      metadatas: (data.metadatas ?? []) as (TMeta | null)[][],\n      uris: data.uris ?? [],\n    });\n  }\n\n  public async modify({\n    name,\n    metadata,\n    configuration,\n  }: {\n    name?: string;\n    metadata?: CollectionMetadata;\n    configuration?: UpdateCollectionConfiguration;\n  }): Promise<void> {\n    if (name) this.name = name;\n\n    if (metadata) {\n      validateMetadata(metadata);\n      this.metadata = metadata;\n    }\n\n    const { updateConfiguration, updateEmbeddingFunction } = configuration\n      ? await processUpdateCollectionConfig({\n        collectionName: this.name,\n        currentConfiguration: this.configuration,\n        newConfiguration: configuration,\n        currentEmbeddingFunction: this.embeddingFunction,\n      })\n      : {};\n\n    if (updateEmbeddingFunction) {\n      this.embeddingFunction = updateEmbeddingFunction;\n    }\n\n    if (updateConfiguration) {\n      this.configuration = {\n        hnsw: { ...this.configuration.hnsw, ...updateConfiguration.hnsw },\n        spann: { ...this.configuration.spann, ...updateConfiguration.spann },\n        embeddingFunction: updateConfiguration.embedding_function,\n      };\n    }\n\n    await Api.updateCollection({\n      client: this.apiClient,\n      path: await this.path(),\n      body: {\n        new_name: name,\n        new_metadata: metadata,\n        new_configuration: updateConfiguration,\n      },\n    });\n  }\n\n  public async fork({ name }: { name: string }): Promise<Collection> {\n    const { data } = await Api.forkCollection({\n      client: this.apiClient,\n      path: await this.path(),\n      body: { new_name: name },\n    });\n\n    return new CollectionImpl({\n      chromaClient: this.chromaClient,\n      apiClient: this.apiClient,\n      name: data.name,\n      id: data.name,\n      embeddingFunction: this._embeddingFunction,\n      metadata: data.metadata ?? undefined,\n      configuration: data.configuration_json,\n    });\n  }\n\n  public async update({\n    ids,\n    embeddings,\n    metadatas,\n    documents,\n    uris,\n  }: {\n    ids: string[];\n    embeddings?: number[][];\n    metadatas?: Metadata[];\n    documents?: string[];\n    uris?: string[];\n  }): Promise<void> {\n    const recordSet: RecordSet = {\n      ids,\n      embeddings,\n      documents,\n      metadatas,\n      uris,\n    };\n\n    const maxBatchSize = await this.chromaClient.getMaxBatchSize();\n\n    await this.prepareRecords({ recordSet, maxBatchSize, update: true });\n\n    const supportsBase64Encoding = await this.chromaClient.supportsBase64Encoding();\n    const embeddingsBase64 = supportsBase64Encoding ? optionalEmbeddingsToBase64Bytes(recordSet.embeddings) : recordSet.embeddings;\n\n    await Api.collectionUpdate({\n      client: this.apiClient,\n      path: await this.path(),\n      body: {\n        ids: recordSet.ids,\n        embeddings: embeddingsBase64,\n        metadatas: recordSet.metadatas,\n        uris: recordSet.uris,\n        documents: recordSet.documents,\n      },\n    });\n  }\n\n  public async upsert({\n    ids,\n    embeddings,\n    metadatas,\n    documents,\n    uris,\n  }: {\n    ids: string[];\n    embeddings?: number[][];\n    metadatas?: Metadata[];\n    documents?: string[];\n    uris?: string[];\n  }): Promise<void> {\n    const recordSet: RecordSet = {\n      ids,\n      embeddings,\n      documents,\n      metadatas,\n      uris,\n    };\n\n    const maxBatchSize = await this.chromaClient.getMaxBatchSize();\n    await this.prepareRecords({ recordSet, maxBatchSize, update: true });\n\n    const supportsBase64Encoding = await this.chromaClient.supportsBase64Encoding();\n    const embeddingsBase64 = supportsBase64Encoding ? optionalEmbeddingsToBase64Bytes(recordSet.embeddings) : recordSet.embeddings;\n\n    await Api.collectionUpsert({\n      client: this.apiClient,\n      path: await this.path(),\n      body: {\n        ids: recordSet.ids,\n        embeddings: embeddingsBase64,\n        metadatas: recordSet.metadatas,\n        uris: recordSet.uris,\n        documents: recordSet.documents,\n      },\n    });\n  }\n\n  public async delete({\n    ids,\n    where,\n    whereDocument,\n  }: {\n    ids?: string[];\n    where?: Where;\n    whereDocument?: WhereDocument;\n  }): Promise<void> {\n    this.validateDelete(ids, where, whereDocument);\n\n    await Api.collectionDelete({\n      client: this.apiClient,\n      path: await this.path(),\n      body: {\n        ids,\n        where,\n        where_document: whereDocument,\n      },\n    });\n  }\n}\n", "export function withChroma(userNextConfig: any = {}): any {\n  const originalWebpackFunction = userNextConfig.webpack;\n\n  const newWebpackFunction = (config: any, options: any): any => {\n    if (!Array.isArray(config.externals)) {\n      config.externals = [];\n    }\n\n    const externalsToAdd = [\"@huggingface/transformers\", \"chromadb\"];\n    for (const ext of externalsToAdd) {\n      if (!config.externals.includes(ext)) {\n        config.externals.push(ext);\n      }\n    }\n\n    if (typeof originalWebpackFunction === \"function\") {\n      return originalWebpackFunction(config, options);\n    }\n    return config;\n  };\n\n  return {\n    ...userNextConfig,\n    webpack: newWebpackFunction,\n  };\n}\n", "import {\n  ChromaClientError,\n  ChromaConnectionError,\n  ChromaForbiddenError,\n  ChromaNotFoundError,\n  ChromaQuotaExceededError,\n  ChromaUnauthorizedError,\n  ChromaUniqueError,\n} from \"./errors\";\n\nconst offlineError = (error: any): boolean => {\n  return Boolean(\n    (error?.name === \"TypeError\" || error?.name === \"FetchError\") &&\n      (error.message?.includes(\"fetch failed\") ||\n        error.message?.includes(\"Failed to fetch\") ||\n        error.message?.includes(\"ENOTFOUND\")),\n  );\n};\n\nexport const chromaFetch: typeof fetch = async (input, init) => {\n  let response: Response;\n  try {\n    response = await fetch(input, init);\n  } catch (err) {\n    if (offlineError(err)) {\n      throw new ChromaConnectionError(\n        \"Failed to connect to chromadb. Make sure your server is running and try again. If you are running from a browser, make sure that your chromadb instance is configured to allow requests from the current origin using the CHROMA_SERVER_CORS_ALLOW_ORIGINS environment variable.\",\n      );\n    }\n    throw new ChromaConnectionError(\"Failed to connect to Chroma\");\n  }\n\n  if (response.ok) {\n    return response;\n  }\n\n  switch (response.status) {\n    case 400:\n      let status = \"Bad Request\";\n      try {\n        const responseBody = await response.json();\n        status = responseBody.message || status;\n      } catch {}\n      throw new ChromaClientError(\n        `Bad request to ${\n          (input as Request).url || \"Chroma\"\n        } with status: ${status}`,\n      );\n    case 401:\n      throw new ChromaUnauthorizedError(`Unauthorized`);\n    case 403:\n      throw new ChromaForbiddenError(\n        `You do not have permission to access the requested resource.`,\n      );\n    case 404:\n      throw new ChromaNotFoundError(\n        `The requested resource could not be found`,\n      );\n    case 409:\n      throw new ChromaUniqueError(\"The resource already exists\");\n    case 422:\n      const body = await response.json();\n      if (body && body?.message.startsWith(\"Quota exceeded\")) {\n        throw new ChromaQuotaExceededError(body?.message);\n      }\n      break;\n  }\n\n  throw new ChromaConnectionError(\n    `Unable to connect to the chromadb server. Please try again later.`,\n  );\n};\n", "import { defaultAdminClientArgs, HttpMethod, normalize<PERSON>ethod } from \"./utils\";\nimport { createClient, createConfig } from \"@hey-api/client-fetch\";\nimport { Database, DefaultService as Api } from \"./api\";\nimport { chromaFetch } from \"./chroma-fetch\";\n\n/**\n * Configuration options for the AdminClient.\n */\nexport interface AdminClientArgs {\n  /** The host address of the Chroma server */\n  host: string;\n  /** The port number of the Chroma server */\n  port: number;\n  /** Whether to use SSL/HTTPS for connections */\n  ssl: boolean;\n  /** Additional HTTP headers to send with requests */\n  headers?: Record<string, string>;\n  /** Additional fetch options for HTTP requests */\n  fetchOptions?: RequestInit;\n}\n\n/**\n * Arguments for listing databases within a tenant.\n */\nexport interface ListDatabasesArgs {\n  /** The tenant name to list databases for */\n  tenant: string;\n  /** Maximum number of databases to return (default: 100) */\n  limit?: number;\n  /** Number of databases to skip (default: 0) */\n  offset?: number;\n}\n\n/**\n * Administrative client for managing ChromaDB tenants and databases.\n * Provides methods for creating, deleting, and listing tenants and databases.\n */\nexport class AdminClient {\n  private readonly apiClient: ReturnType<typeof createClient>;\n\n  /**\n   * Creates a new AdminClient instance.\n   * @param args - Optional configuration for the admin client\n   */\n  constructor(args?: AdminClientArgs) {\n    const { host, port, ssl, headers, fetchOptions } =\n      args || defaultAdminClientArgs;\n\n    const baseUrl = `${ssl ? \"https\" : \"http\"}://${host}:${port}`;\n\n    const configOptions = {\n      ...fetchOptions,\n      method: normalizeMethod(fetchOptions?.method) as HttpMethod,\n      baseUrl,\n      headers,\n    };\n\n    this.apiClient = createClient(createConfig(configOptions));\n    this.apiClient.setConfig({ fetch: chromaFetch });\n  }\n\n  /**\n   * Creates a new database within a tenant.\n   * @param options - Database creation options\n   * @param options.name - Name of the database to create\n   * @param options.tenant - Tenant that will own the database\n   */\n  public async createDatabase({\n    name,\n    tenant,\n  }: {\n    name: string;\n    tenant: string;\n  }): Promise<void> {\n    await Api.createDatabase({\n      client: this.apiClient,\n      path: { tenant },\n      body: { name },\n    });\n  }\n\n  /**\n   * Retrieves information about a specific database.\n   * @param options - Database retrieval options\n   * @param options.name - Name of the database to retrieve\n   * @param options.tenant - Tenant that owns the database\n   * @returns Promise resolving to database information\n   */\n  public async getDatabase({\n    name,\n    tenant,\n  }: {\n    name: string;\n    tenant: string;\n  }): Promise<Database> {\n    const { data } = await Api.getDatabase({\n      client: this.apiClient,\n      path: { tenant, database: name },\n    });\n\n    return data;\n  }\n\n  /**\n   * Deletes a database and all its data.\n   * @param options - Database deletion options\n   * @param options.name - Name of the database to delete\n   * @param options.tenant - Tenant that owns the database\n   * @warning This operation is irreversible and will delete all data\n   */\n  public async deleteDatabase({\n    name,\n    tenant,\n  }: {\n    name: string;\n    tenant: string;\n  }): Promise<void> {\n    await Api.deleteDatabase({\n      client: this.apiClient,\n      path: { tenant, database: name },\n    });\n  }\n\n  /**\n   * Lists all databases within a tenant.\n   * @param args - Listing parameters including tenant and pagination\n   * @returns Promise resolving to an array of database information\n   */\n  public async listDatabases(args: ListDatabasesArgs): Promise<Database[]> {\n    const { limit = 100, offset = 0, tenant } = args;\n    const { data } = await Api.listDatabases({\n      client: this.apiClient,\n      path: { tenant },\n      query: { limit, offset },\n    });\n\n    return data;\n  }\n\n  /**\n   * Creates a new tenant.\n   * @param options - Tenant creation options\n   * @param options.name - Name of the tenant to create\n   */\n  public async createTenant({ name }: { name: string }): Promise<void> {\n    await Api.createTenant({\n      client: this.apiClient,\n      body: { name },\n    });\n  }\n\n  /**\n   * Retrieves information about a specific tenant.\n   * @param options - Tenant retrieval options\n   * @param options.name - Name of the tenant to retrieve\n   * @returns Promise resolving to the tenant name\n   */\n  public async getTenant({ name }: { name: string }): Promise<string> {\n    const { data } = await Api.getTenant({\n      client: this.apiClient,\n      path: { tenant_name: name },\n    });\n\n    return data.name;\n  }\n}\n", "import { createClient, createConfig } from \"@hey-api/client-fetch\";\nimport {\n  defaultChromaClientArgs as defaultArgs,\n  HttpMethod,\n  normalizeMethod,\n  parseConnectionPath,\n} from \"./utils\";\nimport { DefaultService as Api, ChecklistResponse } from \"./api\";\nimport { CollectionMetadata, UserIdentity } from \"./types\";\nimport { Collection, CollectionImpl } from \"./collection\";\nimport { EmbeddingFunction, getEmbeddingFunction } from \"./embedding-function\";\nimport { chromaFetch } from \"./chroma-fetch\";\nimport * as process from \"node:process\";\nimport {\n  ChromaConnectionError,\n  ChromaUnauthorizedError,\n  ChromaValueError,\n} from \"./errors\";\nimport {\n  CreateCollectionConfiguration,\n  processCreateCollectionConfig,\n} from \"./collection-configuration\";\n\n/**\n * Configuration options for the ChromaClient.\n */\nexport interface ChromaClientArgs {\n  /** The host address of the Chroma server. Defaults to 'localhost' */\n  host?: string;\n  /** The port number of the Chroma server. Defaults to 8000 */\n  port?: number;\n  /** Whether to use SSL/HTTPS for connections. Defaults to false */\n  ssl?: boolean;\n  /** The tenant name in the Chroma server to connect to */\n  tenant?: string;\n  /** The database name to connect to */\n  database?: string;\n  /** Additional HTTP headers to send with requests */\n  headers?: Record<string, string>;\n  /** Additional fetch options for HTTP requests */\n  fetchOptions?: RequestInit;\n  /** @deprecated Use host, port, and ssl instead */\n  path?: string;\n  /** @deprecated */\n  auth?: Record<string, string>;\n}\n\n/**\n * Main client class for interacting with ChromaDB.\n * Provides methods for managing collections and performing operations on them.\n */\nexport class ChromaClient {\n  private _tenant: string | undefined;\n  private _database: string | undefined;\n  private _preflightChecks: ChecklistResponse | undefined;\n  private readonly apiClient: ReturnType<typeof createClient>;\n\n  /**\n   * Creates a new ChromaClient instance.\n   * @param args - Configuration options for the client\n   */\n  constructor(args: Partial<ChromaClientArgs> = {}) {\n    let {\n      host = defaultArgs.host,\n      port = defaultArgs.port,\n      ssl = defaultArgs.ssl,\n      tenant = defaultArgs.tenant,\n      database = defaultArgs.database,\n      headers = defaultArgs.headers,\n      fetchOptions = defaultArgs.fetchOptions,\n    } = args;\n\n    if (args.path) {\n      console.warn(\n        \"The 'path' argument is deprecated. Please use 'ssl', 'host', and 'port' instead\",\n      );\n      const parsedPath = parseConnectionPath(args.path);\n      ssl = parsedPath.ssl;\n      host = parsedPath.host;\n      port = parsedPath.port;\n    }\n\n    if (args.auth) {\n      console.warn(\n        \"The 'auth' argument is deprecated. Please use 'headers' instead\",\n      );\n      if (!headers) {\n        headers = {};\n      }\n      if (\n        !headers[\"x-chroma-token\"] &&\n        args.auth.tokenHeaderType === \"X_CHROMA_TOKEN\" &&\n        args.auth.credentials\n      ) {\n        headers[\"x-chroma-token\"] = args.auth.credentials;\n      }\n    }\n\n    const baseUrl = `${ssl ? \"https\" : \"http\"}://${host}:${port}`;\n\n    this._tenant = tenant || process.env.CHROMA_TENANT;\n    this._database = database || process.env.CHROMA_DATABASE;\n\n    const configOptions = {\n      ...fetchOptions,\n      method: normalizeMethod(fetchOptions?.method) as HttpMethod,\n      baseUrl,\n      headers,\n    };\n\n    this.apiClient = createClient(createConfig(configOptions));\n    this.apiClient.setConfig({ fetch: chromaFetch });\n  }\n\n  /**\n   * Gets the current tenant name.\n   * @returns The tenant name or undefined if not set\n   */\n  public get tenant(): string | undefined {\n    return this._tenant;\n  }\n\n  protected set tenant(tenant: string | undefined) {\n    this._tenant = tenant;\n  }\n\n  /**\n   * Gets the current database name.\n   * @returns The database name or undefined if not set\n   */\n  public get database(): string | undefined {\n    return this._database;\n  }\n\n  protected set database(database: string | undefined) {\n    this._database = database;\n  }\n\n  /**\n   * Gets the preflight checks\n   * @returns The preflight checks or undefined if not set\n   */\n  public get preflightChecks(): ChecklistResponse | undefined {\n    return this._preflightChecks;\n  }\n\n  protected set preflightChecks(preflightChecks: ChecklistResponse | undefined) {\n    this._preflightChecks = preflightChecks;\n  }\n\n  /** @ignore */\n  public async _path(): Promise<{ tenant: string; database: string }> {\n    if (!this._tenant || !this._database) {\n      const { tenant, databases } = await this.getUserIdentity();\n      const uniqueDBs = [...new Set(databases)];\n      this._tenant = tenant;\n      if (uniqueDBs.length === 0) {\n        throw new ChromaUnauthorizedError(\n          `Your API key does not have access to any DBs for tenant ${this.tenant}`,\n        );\n      }\n      if (uniqueDBs.length > 1 || uniqueDBs[0] === \"*\") {\n        throw new ChromaValueError(\n          \"Your API key is scoped to more than 1 DB. Please provide a DB name to the CloudClient constructor\",\n        );\n      }\n      this._database = uniqueDBs[0];\n    }\n    return { tenant: this._tenant, database: this._database };\n  }\n\n  /**\n   * Gets the user identity information including tenant and accessible databases.\n   * @returns Promise resolving to user identity data\n   */\n  public async getUserIdentity(): Promise<UserIdentity> {\n    const { data } = await Api.getUserIdentity({\n      client: this.apiClient,\n    });\n    return data;\n  }\n\n  /**\n   * Sends a heartbeat request to check server connectivity.\n   * @returns Promise resolving to the server's nanosecond heartbeat timestamp\n   */\n  public async heartbeat(): Promise<number> {\n    const { data } = await Api.heartbeat({\n      client: this.apiClient,\n    });\n    return data[\"nanosecond heartbeat\"];\n  }\n\n  /**\n   * Lists all collections in the current database.\n   * @param args - Optional pagination parameters\n   * @param args.limit - Maximum number of collections to return (default: 100)\n   * @param args.offset - Number of collections to skip (default: 0)\n   * @returns Promise resolving to an array of Collection instances\n   */\n  public async listCollections(\n    args?: Partial<{\n      limit: number;\n      offset: number;\n    }>,\n  ): Promise<Collection[]> {\n    const { limit = 100, offset = 0 } = args || {};\n\n    const { data } = await Api.listCollections({\n      client: this.apiClient,\n      path: await this._path(),\n      query: { limit, offset },\n    });\n\n    return Promise.all(\n      data.map(\n        async (collection) =>\n          new CollectionImpl({\n            chromaClient: this,\n            apiClient: this.apiClient,\n            name: collection.name,\n            id: collection.id,\n            embeddingFunction: await getEmbeddingFunction(\n              collection.name,\n              collection.configuration_json.embedding_function ?? undefined,\n            ),\n            configuration: collection.configuration_json,\n            metadata: collection.metadata ?? undefined,\n          }),\n      ),\n    );\n  }\n\n  /**\n   * Gets the total number of collections in the current database.\n   * @returns Promise resolving to the collection count\n   */\n  public async countCollections(): Promise<number> {\n    const { data } = await Api.countCollections({\n      client: this.apiClient,\n      path: await this._path(),\n    });\n\n    return data;\n  }\n\n  /**\n   * Creates a new collection with the specified configuration.\n   * @param options - Collection creation options\n   * @param options.name - The name of the collection\n   * @param options.configuration - Optional collection configuration\n   * @param options.metadata - Optional metadata for the collection\n   * @param options.embeddingFunction - Optional embedding function to use. Defaults to `DefaultEmbeddingFunction` from @chroma-core/default-embed\n   * @returns Promise resolving to the created Collection instance\n   * @throws Error if a collection with the same name already exists\n   */\n  public async createCollection({\n    name,\n    configuration,\n    metadata,\n    embeddingFunction,\n  }: {\n    name: string;\n    configuration?: CreateCollectionConfiguration;\n    metadata?: CollectionMetadata;\n    embeddingFunction?: EmbeddingFunction;\n  }): Promise<Collection> {\n    const collectionConfig = await processCreateCollectionConfig({\n      configuration,\n      embeddingFunction,\n    });\n\n    const { data } = await Api.createCollection({\n      client: this.apiClient,\n      path: await this._path(),\n      body: {\n        name,\n        configuration: collectionConfig,\n        metadata,\n        get_or_create: false,\n      },\n    });\n\n    return new CollectionImpl({\n      chromaClient: this,\n      apiClient: this.apiClient,\n      name,\n      configuration: data.configuration_json,\n      metadata,\n      embeddingFunction:\n        embeddingFunction ??\n        (await getEmbeddingFunction(\n          data.name,\n          data.configuration_json.embedding_function ?? undefined,\n        )),\n      id: data.id,\n    });\n  }\n\n  /**\n   * Retrieves an existing collection by name.\n   * @param options - Collection retrieval options\n   * @param options.name - The name of the collection to retrieve\n   * @param options.embeddingFunction - Optional embedding function. Should match the one used to create the collection.\n   * @returns Promise resolving to the Collection instance\n   * @throws Error if the collection does not exist\n   */\n  public async getCollection({\n    name,\n    embeddingFunction,\n  }: {\n    name: string;\n    embeddingFunction?: EmbeddingFunction;\n  }): Promise<Collection> {\n    const { data } = await Api.getCollection({\n      client: this.apiClient,\n      path: { ...(await this._path()), collection_id: name },\n    });\n\n    return new CollectionImpl({\n      chromaClient: this,\n      apiClient: this.apiClient,\n      name,\n      configuration: data.configuration_json,\n      metadata: data.metadata ?? undefined,\n      embeddingFunction: embeddingFunction\n        ? embeddingFunction\n        : await getEmbeddingFunction(\n          data.name,\n          data.configuration_json.embedding_function ?? undefined,\n        ),\n      id: data.id,\n    });\n  }\n\n  /**\n   * Retrieves multiple collections by name.\n   * @param items - Array of collection names or objects with name and optional embedding function (should match the ones used to create the collections)\n   * @returns Promise resolving to an array of Collection instances\n   */\n  public async getCollections(\n    items: string[] | { name: string; embeddingFunction?: EmbeddingFunction }[],\n  ): Promise<Collection[]> {\n    if (items.length === 0) return [];\n\n    let requestedCollections = items;\n    if (typeof items[0] === \"string\") {\n      requestedCollections = (items as string[]).map((item) => {\n        return { name: item, embeddingFunction: undefined };\n      });\n    }\n\n    let collections = requestedCollections as {\n      name: string;\n      embeddingFunction?: EmbeddingFunction;\n    }[];\n\n    return Promise.all(\n      collections.map(async (collection) => {\n        return this.getCollection({ ...collection });\n      }),\n    );\n  }\n\n  /**\n   * Gets an existing collection or creates it if it doesn't exist.\n   * @param options - Collection options\n   * @param options.name - The name of the collection\n   * @param options.configuration - Optional collection configuration (used only if creating)\n   * @param options.metadata - Optional metadata for the collection (used only if creating)\n   * @param options.embeddingFunction - Optional embedding function to use\n   * @returns Promise resolving to the Collection instance\n   */\n  public async getOrCreateCollection({\n    name,\n    configuration,\n    metadata,\n    embeddingFunction,\n  }: {\n    name: string;\n    configuration?: CreateCollectionConfiguration;\n    metadata?: CollectionMetadata;\n    embeddingFunction?: EmbeddingFunction;\n  }): Promise<Collection> {\n    const collectionConfig = await processCreateCollectionConfig({\n      configuration,\n      embeddingFunction,\n    });\n\n    const { data } = await Api.createCollection({\n      client: this.apiClient,\n      path: await this._path(),\n      body: {\n        name,\n        configuration: collectionConfig,\n        metadata,\n        get_or_create: true,\n      },\n    });\n\n    return new CollectionImpl({\n      chromaClient: this,\n      apiClient: this.apiClient,\n      name,\n      configuration: data.configuration_json,\n      metadata: data.metadata ?? undefined,\n      embeddingFunction:\n        embeddingFunction ??\n        (await getEmbeddingFunction(\n          name,\n          data.configuration_json.embedding_function ?? undefined,\n        )),\n      id: data.id,\n    });\n  }\n\n  /**\n   * Deletes a collection and all its data.\n   * @param options - Deletion options\n   * @param options.name - The name of the collection to delete\n   */\n  public async deleteCollection({ name }: { name: string }): Promise<void> {\n    await Api.deleteCollection({\n      client: this.apiClient,\n      path: { ...(await this._path()), collection_id: name },\n    });\n  }\n\n  /**\n   * Resets the entire database, deleting all collections and data.\n   * @returns Promise that resolves when the reset is complete\n   * @warning This operation is irreversible and will delete all data\n   */\n  public async reset(): Promise<void> {\n    await Api.reset({\n      client: this.apiClient,\n    });\n  }\n\n  /**\n   * Gets the version of the Chroma server.\n   * @returns Promise resolving to the server version string\n   */\n  public async version(): Promise<string> {\n    const { data } = await Api.version({\n      client: this.apiClient,\n    });\n    return data;\n  }\n\n  /**\n   * Gets the preflight checks\n   * @returns Promise resolving to the preflight checks\n   */\n  public async getPreflightChecks(): Promise<ChecklistResponse> {\n    if (!this.preflightChecks) {\n      const { data } = await Api.preFlightChecks({\n        client: this.apiClient,\n      });\n      this.preflightChecks = data;\n      return this.preflightChecks;\n    }\n    return this.preflightChecks;\n  }\n\n  /**\n   * Gets the max batch size\n   * @returns Promise resolving to the max batch size\n   */\n  public async getMaxBatchSize(): Promise<number> {\n    const preflightChecks = await this.getPreflightChecks();\n    return preflightChecks.max_batch_size ?? -1;\n  }\n\n  /**\n   * Gets whether base64_encoding is supported by the connected server\n   * @returns Promise resolving to whether base64_encoding is supported\n   */\n  public async supportsBase64Encoding(): Promise<boolean> {\n    const preflightChecks = await this.getPreflightChecks();\n    return preflightChecks.supports_base64_encoding ?? false;\n  }\n}\n", "import { ChromaClient } from \"./chroma-client\";\nimport * as process from \"node:process\";\nimport { AdminClient } from \"./admin-client\";\nimport { ChromaUnauthorizedError, ChromaValueError } from \"./errors\";\n\n/**\n * ChromaDB cloud client for connecting to hosted Chroma instances.\n * Extends ChromaClient with cloud-specific authentication and configuration.\n */\nexport class CloudClient extends ChromaClient {\n  /**\n   * Creates a new CloudClient instance for Chroma Cloud.\n   * @param args - Cloud client configuration options\n   */\n  constructor(\n    args: Partial<{\n      /** API key for authentication (or set CHROMA_API_KEY env var) */\n      apiKey?: string;\n      /** Tenant name for multi-tenant deployments */\n      tenant?: string;\n      /** Database name to connect to */\n      database?: string;\n      /** Additional fetch options for HTTP requests */\n      fetchOptions?: RequestInit;\n    }> = {},\n  ) {\n    const apiKey = args.apiKey || process.env.CHROMA_API_KEY;\n    if (!apiKey) {\n      throw new ChromaValueError(\n        \"Missing API key. Please provide it to the CloudClient constructor or set your CHROMA_API_KEY environment variable\",\n      );\n    }\n\n    const tenant = args.tenant || process.env.CHROMA_TENANT;\n    const database = args.database || process.env.CHROMA_DATABASE;\n\n    super({\n      host: \"api.trychroma.com\",\n      port: 8000,\n      ssl: true,\n      tenant,\n      database,\n      headers: { \"x-chroma-token\": apiKey },\n      fetchOptions: args.fetchOptions,\n    });\n\n    // Override from ChromaClient construction in case undefined. This will trigger auto-resolution in the \"path\" function\n    this.tenant = tenant;\n    this.database = database;\n  }\n}\n\n/**\n * Admin client for Chroma Cloud administrative operations.\n * Extends AdminClient with cloud-specific authentication.\n */\nexport class AdminCloudClient extends AdminClient {\n  /**\n   * Creates a new AdminCloudClient instance for cloud admin operations.\n   * @param args - Admin cloud client configuration options\n   */\n  constructor(\n    args: Partial<{\n      /** API key for authentication (or set CHROMA_API_KEY env var) */\n      apiKey?: string;\n      /** Additional fetch options for HTTP requests */\n      fetchOptions?: RequestInit;\n    }> = {},\n  ) {\n    const apiKey = args.apiKey || process.env.CHROMA_API_KEY;\n    if (!apiKey) {\n      throw new ChromaValueError(\n        \"Missing API key. Please provide it to the CloudClient constructor or set your CHROMA_API_KEY environment variable\",\n      );\n    }\n\n    super({\n      host: \"api.trychroma.com\",\n      port: 8000,\n      ssl: true,\n      headers: { \"x-chroma-token\": apiKey },\n      fetchOptions: args.fetchOptions,\n    });\n  }\n}\n"], "mappings": ";;;;;AAGA,IAAI,OAAQ,WAAmB,SAAS,aAAa;AAEnD,QAAM,kBAAkB,WAAW;AAGnC,QAAM,iBAAiB,SACrB,OACA,MACA;AACA,QAAI,QAAQ,OAAO,SAAS,UAAU;AACpC,YAAM,YAAY,EAAE,GAAG,KAAK;AAC5B,UAAI,YAAY,WAAW;AACzB,eAAQ,UAAkB;AAAA,MAC5B;AACA,aAAO,IAAI,gBAAgB,OAAO,SAAS;AAAA,IAC7C;AACA,WAAO,IAAI,gBAAgB,OAAO,IAAI;AAAA,EACxC;AAGA,SAAO,eAAe,gBAAgB,eAAe;AACrD,SAAO,eAAe,gBAAgB,aAAa;AAAA,IACjD,OAAO,gBAAgB;AAAA,IACvB,UAAU;AAAA,EACZ,CAAC;AAGD,aAAW,UAAU;AACvB;;;ACKO,IAAM,sBAAsB;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAUO,IAAM,kBAAkB,CAAC,GAAG,qBAAqB,KAAK;AAmEtD,IAAK,cAAL,kBAAKA,iBAAL;AAEL,EAAAA,aAAA,eAAY;AAEZ,EAAAA,aAAA,eAAY;AAEZ,EAAAA,aAAA,gBAAa;AAEb,EAAAA,aAAA,eAAY;AAEZ,EAAAA,aAAA,UAAO;AAVG,SAAAA;AAAA,GAAA;AAiBL,IAAM,YAAN,MAAmD;AAAA;AAAA;AAAA;AAAA;AAAA,EAYxD,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAOG;AACD,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,MAAM;AACX,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,OAAO;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMO,OAAO;AACZ,WAAO,KAAK,IAAI,IAAI,CAAC,IAAI,UAAU;AACjC,aAAO;AAAA,QACL;AAAA,QACA,UAAU,KAAK,QAAQ,SAAS,WAAW,IACvC,KAAK,UAAU,KAAK,IACpB;AAAA,QACJ,WAAW,KAAK,QAAQ,SAAS,YAAY,IACzC,KAAK,WAAW,KAAK,IACrB;AAAA,QACJ,UAAU,KAAK,QAAQ,SAAS,WAAW,IACvC,KAAK,UAAU,KAAK,IACpB;AAAA,QACJ,KAAK,KAAK,QAAQ,SAAS,MAAM,IAAI,KAAK,KAAK,KAAK,IAAI;AAAA,MAC1D;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAyBO,IAAM,cAAN,MAAqD;AAAA;AAAA;AAAA;AAAA;AAAA,EAa1D,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAQG;AACD,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,MAAM;AACX,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,OAAO;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMO,OAAkC;AACvC,UAAM,UAOE,CAAC;AAET,aAASC,KAAI,GAAGA,KAAI,KAAK,IAAI,QAAQA,MAAK;AACxC,YAAM,UAAU,KAAK,IAAIA,EAAC,EAAE,IAAI,CAAC,IAAI,UAAU;AAC7C,eAAO;AAAA,UACL;AAAA,UACA,UAAU,KAAK,QAAQ,SAAS,WAAW,IACvC,KAAK,UAAUA,EAAC,EAAE,KAAK,IACvB;AAAA,UACJ,WAAW,KAAK,QAAQ,SAAS,YAAY,IACzC,KAAK,WAAWA,EAAC,EAAE,KAAK,IACxB;AAAA,UACJ,UAAU,KAAK,QAAQ,SAAS,WAAW,IACvC,KAAK,UAAUA,EAAC,EAAE,KAAK,IACvB;AAAA,UACJ,KAAK,KAAK,QAAQ,SAAS,MAAM,IAAI,KAAK,KAAKA,EAAC,EAAE,KAAK,IAAI;AAAA,UAC3D,UAAU,KAAK,QAAQ,SAAS,WAAW,IACvC,KAAK,UAAUA,EAAC,EAAE,KAAK,IACvB;AAAA,QACN;AAAA,MACF,CAAC;AAED,cAAQ,KAAK,OAAO;AAAA,IACtB;AAEA,WAAO;AAAA,EACT;AACF;;;ACpRaC,IAAAA,IAAe,OAC1BC,GACAC,MACgC;AAChC,MAAMC,IACJ,OAAOD,KAAa,aAAa,MAAMA,EAASD,CAAI,IAAIC;AAE1D,MAAKC,EAIL,QAAIF,EAAK,WAAW,WACX,UAAUE,CAAK,KAGpBF,EAAK,WAAW,UACX,SAAS,KAAKE,CAAK,CAAC,KAGtBA;AACT;AApBaC,ICsCAC,IAAqB,EAChC,gBAAoBC,OAClB,KAAK,UAAUA,GAAM,CAACC,GAAKC,MACzB,OAAOA,KAAU,WAAWA,EAAM,SAAA,IAAaA,CACjD,EACJ;AD3CaC,IEQAC,IAAyBC,OAA+B;AACnE,UAAQA,GAAAA;IACN,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT;AACE,aAAO;EACX;AACF;AFnBaF,IEqBAG,IAA2BD,OAA+B;AACrE,UAAQA,GAAAA;IACN,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT;AACE,aAAO;EACX;AACF;AFhCaF,IEkCAI,IAA0BF,OAAgC;AACrE,UAAQA,GAAAA;IACN,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT;AACE,aAAO;EACX;AACF;AF7CaF,IE+CAK,IAAsB,CAAC,EAClC,eAAAC,GACA,SAAAC,GACA,MAAAC,GACA,OAAAN,GACA,OAAAO,EACF,MAEM;AACJ,MAAI,CAACF,GAAS;AACZ,QAAMG,KACJJ,IAAgBG,IAAQA,EAAM,IAAKE,OAAM,mBAAmBA,CAAW,CAAC,GACxE,KAAKR,EAAwBD,CAAK,CAAC;AACrC,YAAQA,GAAAA;MACN,KAAK;AACH,eAAO,IAAIQ,CAAY;MACzB,KAAK;AACH,eAAO,IAAIF,CAAI,IAAIE,CAAY;MACjC,KAAK;AACH,eAAOA;MACT;AACE,eAAO,GAAGF,CAAI,IAAIE,CAAY;IAClC;EACF;AAEA,MAAME,IAAYX,EAAsBC,CAAK,GACvCQ,IAAeD,EAClB,IAAKE,OACAT,MAAU,WAAWA,MAAU,WAC1BI,IAAgBK,IAAI,mBAAmBA,CAAW,IAGpDE,EAAwB,EAC7B,eAAAP,GACA,MAAAE,GACA,OAAOG,EACT,CAAC,CACF,EACA,KAAKC,CAAS;AACjB,SAAOV,MAAU,WAAWA,MAAU,WAClCU,IAAYF,IACZA;AACN;AFzFaV,IE2FAa,IAA0B,CAAC,EACtC,eAAAP,GACA,MAAAE,GACA,OAAAC,EACF,MAA+B;AAC7B,MAA2BA,KAAU,KACnC,QAAO;AAGT,MAAI,OAAOA,KAAU,SACnB,OAAM,IAAI,MACR,2GACF;AAGF,SAAO,GAAGD,CAAI,IAAIF,IAAgBG,IAAQ,mBAAmBA,CAAK,CAAC;AACrE;AF3GaT,IE6GAc,IAAuB,CAAC,EACnC,eAAAR,GACA,SAAAC,GACA,MAAAC,GACA,OAAAN,GACA,OAAAO,EACF,MAEM;AACJ,MAAIA,aAAiB,KACnB,QAAO,GAAGD,CAAI,IAAIC,EAAM,YAAY,CAAC;AAGvC,MAAIP,MAAU,gBAAgB,CAACK,GAAS;AACtC,QAAIQ,IAAmB,CAAA;AACvB,WAAO,QAAQN,CAAK,EAAE,QAAQ,CAAC,CAACO,GAAKL,CAAC,MAAM;AAC1CI,UAAS,CACP,GAAGA,GACHC,GACAV,IAAiBK,IAAe,mBAAmBA,CAAW,CAChE;IACF,CAAC;AACD,QAAMD,IAAeK,EAAO,KAAK,GAAG;AACpC,YAAQb,GAAAA;MACN,KAAK;AACH,eAAO,GAAGM,CAAI,IAAIE,CAAY;MAChC,KAAK;AACH,eAAO,IAAIA,CAAY;MACzB,KAAK;AACH,eAAO,IAAIF,CAAI,IAAIE,CAAY;MACjC;AACE,eAAOA;IACX;EACF;AAEA,MAAME,IAAYR,EAAuBF,CAAK,GACxCQ,IAAe,OAAO,QAAQD,CAAK,EACtC,IAAI,CAAC,CAACO,GAAKL,CAAC,MACXE,EAAwB,EACtB,eAAAP,GACA,MAAMJ,MAAU,eAAe,GAAGM,CAAI,IAAIQ,CAAG,MAAMA,GACnD,OAAOL,EACT,CAAC,CACH,EACC,KAAKC,CAAS;AACjB,SAAOV,MAAU,WAAWA,MAAU,WAClCU,IAAYF,IACZA;AACN;AC7JA,IAAMO,IAAgB;AAAtB,IAMMC,IAAwB,CAAC,EAAE,MAAAC,GAAM,KAAKC,EAAK,MAAsB;AACrE,MAAIC,IAAMD,GACJE,IAAUF,EAAK,MAAMH,CAAa;AACxC,MAAIK,EACF,UAAWC,KAASD,GAAS;AAC3B,QAAIf,IAAU,OACVC,IAAOe,EAAM,UAAU,GAAGA,EAAM,SAAS,CAAC,GAC1CrB,IAA6B;AAE7BM,MAAK,SAAS,GAAG,MACnBD,IAAU,MACVC,IAAOA,EAAK,UAAU,GAAGA,EAAK,SAAS,CAAC,IAGtCA,EAAK,WAAW,GAAG,KACrBA,IAAOA,EAAK,UAAU,CAAC,GACvBN,IAAQ,WACCM,EAAK,WAAW,GAAG,MAC5BA,IAAOA,EAAK,UAAU,CAAC,GACvBN,IAAQ;AAGV,QAAMO,IAAQU,EAAKX,CAAI;AAEvB,QAA2BC,KAAU,KACnC;AAGF,QAAI,MAAM,QAAQA,CAAK,GAAG;AACxBY,UAAMA,EAAI,QACRE,GACAC,EAAoB,EAAE,SAAAjB,GAAS,MAAAC,GAAM,OAAAN,GAAO,OAAAO,EAAM,CAAC,CACrD;AACA;IACF;AAEA,QAAI,OAAOA,KAAU,UAAU;AAC7BY,UAAMA,EAAI,QACRE,GACAE,EAAqB,EACnB,SAAAlB,GACA,MAAAC,GACA,OAAAN,GACA,OAAOO,EACT,CAAC,CACH;AACA;IACF;AAEA,QAAIP,MAAU,UAAU;AACtBmB,UAAMA,EAAI,QACRE,GACA,IAAIG,EAAwB,EAC1B,MAAAlB,GACA,OAAOC,EACT,CAAC,CAAC,EACJ;AACA;IACF;AAEA,QAAMkB,IAAe,mBACnBzB,MAAU,UAAU,IAAIO,CAAe,KAAMA,CAC/C;AACAY,QAAMA,EAAI,QAAQE,GAAOI,CAAY;EACvC;AAEF,SAAON;AACT;AAzEA,IA2EaO,IAAwB,CAAc,EACjD,eAAAtB,GACA,OAAAuB,GACA,QAAAC,EACF,IAA4B,CAAA,MACDC,OAAmB;AAC1C,MAAIC,IAAmB,CAAA;AACvB,MAAID,KAAe,OAAOA,KAAgB,SACxC,UAAWvB,KAAQuB,GAAa;AAC9B,QAAMtB,IAAQsB,EAAYvB,CAAI;AAE9B,QAA2BC,KAAU,MAIrC;AAAI,UAAA,MAAM,QAAQA,CAAK,GAAG;AACxBuB,YAAS,CACP,GAAGA,GACHR,EAAoB,EAClB,eAAAlB,GACA,SAAS,MACT,MAAAE,GACA,OAAO,QACP,OAAAC,GACA,GAAGoB,EACL,CAAC,CACH;AACA;MACF;AAEA,UAAI,OAAOpB,KAAU,UAAU;AAC7BuB,YAAS,CACP,GAAGA,GACHP,EAAqB,EACnB,eAAAnB,GACA,SAAS,MACT,MAAAE,GACA,OAAO,cACP,OAAOC,GACP,GAAGqB,EACL,CAAC,CACH;AACA;MACF;AAEAE,UAAS,CACP,GAAGA,GACHN,EAAwB,EACtB,eAAApB,GACA,MAAAE,GACA,OAAOC,EACT,CAAC,CACH;IAAA;EACF;AAEF,SAAOuB,EAAO,KAAK,GAAG;AACxB;AAnIF,IA0IaC,IACXC,OACuC;AACvC,MAAI,CAACA,EAGH,QAAO;AAGT,MAAMC,IAAeD,EAAY,MAAM,GAAG,EAAE,CAAC,GAAG,KAAK;AAErD,MAAKC,GAIL;AAAA,QACEA,EAAa,WAAW,kBAAkB,KAC1CA,EAAa,SAAS,OAAO,EAE7B,QAAO;AAGT,QAAIA,MAAiB,sBACnB,QAAO;AAGT,QACE,CAAC,gBAAgB,UAAU,UAAU,QAAQ,EAAE,KAAMC,OACnDD,EAAa,WAAWC,CAAI,CAC9B,EAEA,QAAO;AAGT,QAAID,EAAa,WAAW,OAAO,EACjC,QAAO;EAAA;AAEX;AA/KA,IAiLaE,IAAgB,OAAO,EAClC,UAAAC,GACA,GAAGC,EACL,MAGQ;AACN,WAAWC,KAAQF,GAAU;AAC3B,QAAMG,IAAQ,MAAMC,EAAaF,GAAMD,EAAQ,IAAI;AAEnD,QAAI,CAACE,EACH;AAGF,QAAMjC,IAAOgC,EAAK,QAAQ;AAE1B,YAAQA,EAAK,IAAI;MACf,KAAK;AACED,UAAQ,UACXA,EAAQ,QAAQ,CAAA,IAElBA,EAAQ,MAAM/B,CAAI,IAAIiC;AACtB;MACF,KAAK;AACHF,UAAQ,QAAQ,OAAO,UAAU,GAAG/B,CAAI,IAAIiC,CAAK,EAAE;AACnD;MACF,KAAK;MACL;AACEF,UAAQ,QAAQ,IAAI/B,GAAMiC,CAAK;AAC/B;IACJ;AAEA;EACF;AACF;AAnNA,IAqNaE,IAAgCJ,OAC/BK,EAAO,EACjB,SAASL,EAAQ,SACjB,MAAMA,EAAQ,MACd,OAAOA,EAAQ,OACf,iBACE,OAAOA,EAAQ,mBAAoB,aAC/BA,EAAQ,kBACRX,EAAsBW,EAAQ,eAAe,GACnD,KAAKA,EAAQ,IACf,CAAC;AA/NH,IAmOaK,IAAS,CAAC,EACrB,SAAAC,GACA,MAAA1B,GACA,OAAA2B,GACA,iBAAAC,GACA,KAAK3B,EACP,MAMM;AACJ,MAAM4B,IAAU5B,EAAK,WAAW,GAAG,IAAIA,IAAO,IAAIA,CAAI,IAClDC,KAAOwB,KAAW,MAAMG;AACxB7B,QACFE,IAAMH,EAAsB,EAAE,MAAAC,GAAM,KAAAE,EAAI,CAAC;AAE3C,MAAIW,IAASc,IAAQC,EAAgBD,CAAK,IAAI;AAC9C,SAAId,EAAO,WAAW,GAAG,MACvBA,IAASA,EAAO,UAAU,CAAC,IAEzBA,MACFX,KAAO,IAAIW,CAAM,KAEZX;AACT;AA7PA,IA+Pa4B,IAAe,CAACC,GAAWC,MAAsB;AAC5D,MAAMC,IAAS,EAAE,GAAGF,GAAG,GAAGC,EAAE;AAC5B,SAAIC,EAAO,SAAS,SAAS,GAAG,MAC9BA,EAAO,UAAUA,EAAO,QAAQ,UAAU,GAAGA,EAAO,QAAQ,SAAS,CAAC,IAExEA,EAAO,UAAUC,EAAaH,EAAE,SAASC,EAAE,OAAO,GAC3CC;AACT;AAtQA,IAwQaC,IAAe,IACvBC,MACS;AACZ,MAAMC,IAAgB,IAAI;AAC1B,WAAWC,KAAUF,GAAS;AAC5B,QAAI,CAACE,KAAU,OAAOA,KAAW,SAC/B;AAGF,QAAMC,IACJD,aAAkB,UAAUA,EAAO,QAAA,IAAY,OAAO,QAAQA,CAAM;AAEtE,aAAW,CAACxC,GAAKP,CAAK,KAAKgD,EACzB,KAAIhD,MAAU,KACZ8C,GAAc,OAAOvC,CAAG;aACf,MAAM,QAAQP,CAAK,EAC5B,UAAWE,KAAKF,EACd8C,GAAc,OAAOvC,GAAKL,CAAW;QAE9BF,OAAU,UAGnB8C,EAAc,IACZvC,GACA,OAAOP,KAAU,WAAW,KAAK,UAAUA,CAAK,IAAKA,CACvD;EAGN;AACA,SAAO8C;AACT;AAtSA,IA0TMG,IAAN,MAAgC;EAG9B,cAAc;AAFd;AAGE,SAAK,OAAO,CAAA;EACd;EAEA,QAAQ;AACN,SAAK,OAAO,CAAA;EACd;EAEA,OAAOC,GAAiB;AACtB,WAAO,KAAK,KAAK,QAAQA,CAAE,MAAM;EACnC;EAEA,MAAMA,GAAiB;AACrB,QAAMC,IAAQ,KAAK,KAAK,QAAQD,CAAE;AAC9BC,UAAU,OACZ,KAAK,OAAO,CAAC,GAAG,KAAK,KAAK,MAAM,GAAGA,CAAK,GAAG,GAAG,KAAK,KAAK,MAAMA,IAAQ,CAAC,CAAC;EAE5E;EAEA,IAAID,GAAiB;AACnB,SAAK,OAAO,CAAC,GAAG,KAAK,MAAMA,CAAE;EAC/B;AACF;AAnVA,IAoWaE,IAAqB,OAA+B,EAC/D,OAAO,IAAIH,KACX,SAAS,IAAIA,KACb,UAAU,IAAIA,IAChB;AAxWA,IA0WMI,IAAyBlC,EAAsB,EACnD,eAAe,OACf,OAAO,EACL,SAAS,MACT,OAAO,OACT,GACA,QAAQ,EACN,SAAS,MACT,OAAO,aACT,EACF,CAAC;AApXD,IAsXMmC,IAAiB,EACrB,gBAAgB,mBAClB;AAxXA,IA0XaC,IAAe,CAC1BC,IAAqD,CAAA,OACP,EAC9C,GAAGC,GACH,SAASH,GACT,SAAS,QACT,iBAAiBD,GACjB,GAAGG,EACL;ACrYaE,IAAAA,IAAe,CAACf,IAAiB,CAAA,MAAe;AAC3D,MAAIgB,IAAUnB,EAAae,EAAa,GAAGZ,CAAM,GAE3CiB,IAAY,OAAe,EAAE,GAAGD,EAAQ,IAExCE,IAAalB,QACjBgB,IAAUnB,EAAamB,GAAShB,CAAM,GAC/BiB,EAAAA,IAGHE,IAAeV,EAAAA,GAQfW,IAA6B,OAAOjC,MAAY;AACpD,QAAMkC,IAAO,EACX,GAAGL,GACH,GAAG7B,GACH,OAAOA,EAAQ,SAAS6B,EAAQ,SAAS,WAAW,OACpD,SAASf,EAAae,EAAQ,SAAS7B,EAAQ,OAAO,EACxD;AAEIkC,MAAK,YACP,MAAMpC,EAAc,EAClB,GAAGoC,GACH,UAAUA,EAAK,SACjB,CAAC,GAGCA,EAAK,QAAQA,EAAK,mBACpBA,EAAK,OAAOA,EAAK,eAAeA,EAAK,IAAI,KAIvCA,EAAK,SAAS,UAAaA,EAAK,SAAS,OAC3CA,EAAK,QAAQ,OAAO,cAAc;AAGpC,QAAMpD,IAAMsB,EAAS8B,CAAI,GACnBC,IAAuB,EAC3B,UAAU,UACV,GAAGD,EACL,GAEID,IAAU,IAAI,QAAQnD,GAAKqD,CAAW;AAE1C,aAAWf,KAAMY,EAAa,QAAQ,KACpCC,KAAU,MAAMb,EAAGa,GAASC,CAAI;AAKlC,QAAME,IAASF,EAAK,OAChBG,IAAW,MAAMD,EAAOH,CAAO;AAEnC,aAAWb,KAAMY,EAAa,SAAS,KACrCK,KAAW,MAAMjB,EAAGiB,GAAUJ,GAASC,CAAI;AAG7C,QAAMI,IAAS,EACb,SAAAL,GACA,UAAAI,EACF;AAEA,QAAIA,EAAS,IAAI;AACf,UACEA,EAAS,WAAW,OACpBA,EAAS,QAAQ,IAAI,gBAAgB,MAAM,IAE3C,QAAO,EACL,MAAM,CAAA,GACN,GAAGC,EACL;AAGF,UAAMC,KACHL,EAAK,YAAY,SACdxC,EAAW2C,EAAS,QAAQ,IAAI,cAAc,CAAC,IAC/CH,EAAK,YAAY;AAEvB,UAAIK,MAAY,SACd,QAAO,EACL,MAAMF,EAAS,MACf,GAAGC,EACL;AAGF,UAAIE,IAAO,MAAMH,EAASE,CAAO,EAAA;AACjC,aAAIA,MAAY,WACVL,EAAK,qBACP,MAAMA,EAAK,kBAAkBM,CAAI,GAG/BN,EAAK,wBACPM,IAAO,MAAMN,EAAK,oBAAoBM,CAAI,KAIvC,EACL,MAAAA,GACA,GAAGF,EACL;IACF;AAEA,QAAIG,IAAQ,MAAMJ,EAAS,KAAA;AAE3B,QAAI;AACFI,UAAQ,KAAK,MAAMA,CAAK;IAC1B,QAAQ;IAAA;AAIR,QAAIC,IAAaD;AAEjB,aAAWrB,KAAMY,EAAa,MAAM,KAClCU,KAAc,MAAMtB,EAAGqB,GAAOJ,GAAUJ,GAASC,CAAI;AAKvD,QAFAQ,IAAaA,KAAe,CAAA,GAExBR,EAAK,aACP,OAAMQ;AAGR,WAAO,EACL,OAAOA,GACP,GAAGJ,EACL;EACF;AAEA,SAAO,EACL,UAAAlC,GACA,SAAUJ,OAAYiC,EAAQ,EAAE,GAAGjC,GAAS,QAAQ,UAAU,CAAC,GAC/D,QAASA,OAAYiC,EAAQ,EAAE,GAAGjC,GAAS,QAAQ,SAAS,CAAC,GAC7D,KAAMA,OAAYiC,EAAQ,EAAE,GAAGjC,GAAS,QAAQ,MAAM,CAAC,GACvD,WAAA8B,GACA,MAAO9B,OAAYiC,EAAQ,EAAE,GAAGjC,GAAS,QAAQ,OAAO,CAAC,GACzD,cAAAgC,GACA,SAAUhC,OAAYiC,EAAQ,EAAE,GAAGjC,GAAS,QAAQ,UAAU,CAAC,GAC/D,OAAQA,OAAYiC,EAAQ,EAAE,GAAGjC,GAAS,QAAQ,QAAQ,CAAC,GAC3D,MAAOA,OAAYiC,EAAQ,EAAE,GAAGjC,GAAS,QAAQ,OAAO,CAAC,GACzD,KAAMA,OAAYiC,EAAQ,EAAE,GAAGjC,GAAS,QAAQ,MAAM,CAAC,GACvD,SAAAiC,GACA,WAAAF,GACA,OAAQ/B,OAAYiC,EAAQ,EAAE,GAAGjC,GAAS,QAAQ,QAAQ,CAAC,EAC7D;AACF;;;ACvJO,IAAM,SAAS,EAAa,EAA4B;AAAA,EAC3D,SAAS;AAAA,EACT,cAAc;AAClB,CAAC,CAAC;;;ACEK,IAAM,iBAAN,MAAqB;AAAA;AAAA;AAAA;AAAA,EAIxB,OAAc,gBAAqD,SAAsD;AACrH,YAAQ,SAAS,UAAU,QAAe,IAAkE;AAAA,MACxG,KAAK;AAAA,MACL,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,OAAc,YAAiD,SAAkD;AAC7G,YAAQ,SAAS,UAAU,QAAe,IAAyD;AAAA,MAC/F,KAAK;AAAA,MACL,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,OAAc,UAA+C,SAAgD;AACzG,YAAQ,SAAS,UAAU,QAAe,IAAsD;AAAA,MAC5F,KAAK;AAAA,MACL,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,OAAc,gBAAqD,SAAsD;AACrH,YAAQ,SAAS,UAAU,QAAe,IAAiE;AAAA,MACvG,KAAK;AAAA,MACL,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,OAAc,MAA2C,SAA4C;AACjG,YAAQ,SAAS,UAAU,QAAe,KAA8C;AAAA,MACpF,KAAK;AAAA,MACL,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,OAAc,aAAkD,SAAkD;AAC9G,YAAQ,QAAQ,UAAU,QAAe,KAA6D;AAAA,MAClG,KAAK;AAAA,MACL,GAAG;AAAA,MACH,SAAS;AAAA,QACL,gBAAgB;AAAA,QAChB,GAAG,SAAS;AAAA,MAChB;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,OAAc,UAA+C,SAA+C;AACxG,YAAQ,QAAQ,UAAU,QAAe,IAAsD;AAAA,MAC3F,KAAK;AAAA,MACL,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,OAAc,cAAmD,SAAmD;AAChH,YAAQ,QAAQ,UAAU,QAAe,IAA6D;AAAA,MAClG,KAAK;AAAA,MACL,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,OAAc,eAAoD,SAAoD;AAClH,YAAQ,QAAQ,UAAU,QAAe,KAAiE;AAAA,MACtG,KAAK;AAAA,MACL,GAAG;AAAA,MACH,SAAS;AAAA,QACL,gBAAgB;AAAA,QAChB,GAAG,SAAS;AAAA,MAChB;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,OAAc,eAAoD,SAAoD;AAClH,YAAQ,QAAQ,UAAU,QAAe,OAAmE;AAAA,MACxG,KAAK;AAAA,MACL,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,OAAc,YAAiD,SAAiD;AAC5G,YAAQ,QAAQ,UAAU,QAAe,IAAyD;AAAA,MAC9F,KAAK;AAAA,MACL,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,OAAc,gBAAqD,SAAqD;AACpH,YAAQ,QAAQ,UAAU,QAAe,IAAiE;AAAA,MACtG,KAAK;AAAA,MACL,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,OAAc,iBAAsD,SAAsD;AACtH,YAAQ,QAAQ,UAAU,QAAe,KAAoE;AAAA,MACzG,KAAK;AAAA,MACL,GAAG;AAAA,MACH,SAAS;AAAA,QACL,gBAAgB;AAAA,QAChB,GAAG,SAAS;AAAA,MAChB;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,OAAc,iBAAsD,SAAsD;AACtH,YAAQ,QAAQ,UAAU,QAAe,OAAsE;AAAA,MAC3G,KAAK;AAAA,MACL,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,OAAc,cAAmD,SAAmD;AAChH,YAAQ,QAAQ,UAAU,QAAe,IAA6D;AAAA,MAClG,KAAK;AAAA,MACL,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,OAAc,iBAAsD,SAAsD;AACtH,YAAQ,QAAQ,UAAU,QAAe,IAAoE;AAAA,MACzG,KAAK;AAAA,MACL,GAAG;AAAA,MACH,SAAS;AAAA,QACL,gBAAgB;AAAA,QAChB,GAAG,SAAS;AAAA,MAChB;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,OAAc,cAAmD,SAAmD;AAChH,YAAQ,QAAQ,UAAU,QAAe,KAAmD;AAAA,MACxF,KAAK;AAAA,MACL,GAAG;AAAA,MACH,SAAS;AAAA,QACL,gBAAgB;AAAA,QAChB,GAAG,SAAS;AAAA,MAChB;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,OAAc,gBAAqD,SAAqD;AACpH,YAAQ,QAAQ,UAAU,QAAe,IAAiE;AAAA,MACtG,KAAK;AAAA,MACL,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,OAAc,iBAAsD,SAAsD;AACtH,YAAQ,QAAQ,UAAU,QAAe,KAAoE;AAAA,MACzG,KAAK;AAAA,MACL,GAAG;AAAA,MACH,SAAS;AAAA,QACL,gBAAgB;AAAA,QAChB,GAAG,SAAS;AAAA,MAChB;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,OAAc,eAAoD,SAAoD;AAClH,YAAQ,QAAQ,UAAU,QAAe,KAAgE;AAAA,MACrG,KAAK;AAAA,MACL,GAAG;AAAA,MACH,SAAS;AAAA,QACL,gBAAgB;AAAA,QAChB,GAAG,SAAS;AAAA,MAChB;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,OAAc,cAAmD,SAAmD;AAChH,YAAQ,QAAQ,UAAU,QAAe,KAA8D;AAAA,MACnG,KAAK;AAAA,MACL,GAAG;AAAA,MACH,SAAS;AAAA,QACL,gBAAgB;AAAA,QAChB,GAAG,SAAS;AAAA,MAChB;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,OAAc,gBAAqD,SAAqD;AACpH,YAAQ,QAAQ,UAAU,QAAe,KAAkE;AAAA,MACvG,KAAK;AAAA,MACL,GAAG;AAAA,MACH,SAAS;AAAA,QACL,gBAAgB;AAAA,QAChB,GAAG,SAAS;AAAA,MAChB;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,OAAc,iBAAsD,SAAsD;AACtH,YAAQ,QAAQ,UAAU,QAAe,KAAsD;AAAA,MAC3F,KAAK;AAAA,MACL,GAAG;AAAA,MACH,SAAS;AAAA,QACL,gBAAgB;AAAA,QAChB,GAAG,SAAS;AAAA,MAChB;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,OAAc,iBAAsD,SAAsD;AACtH,YAAQ,QAAQ,UAAU,QAAe,KAAoE;AAAA,MACzG,KAAK;AAAA,MACL,GAAG;AAAA,MACH,SAAS;AAAA,QACL,gBAAgB;AAAA,QAChB,GAAG,SAAS;AAAA,MAChB;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,OAAc,iBAAsD,SAAsD;AACtH,YAAQ,QAAQ,UAAU,QAAe,IAAmE;AAAA,MACxG,KAAK;AAAA,MACL,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAKA,OAAc,QAA6C,SAA8C;AACrG,YAAQ,SAAS,UAAU,QAAe,IAA4C;AAAA,MAClF,KAAK;AAAA,MACL,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAEJ;;;AClUO,IAAM,cAAN,cAA0B,MAAM;AAAA,EACrC,YAAY,MAAc,SAAiC,OAAiB;AAC1E,UAAM,OAAO;AAD4C;AAEzD,SAAK,OAAO;AAAA,EACd;AACF;AAKO,IAAM,wBAAN,cAAoC,MAAM;AAAA,EAE/C,YAAY,SAAiC,OAAiB;AAC5D,UAAM,OAAO;AAD8B;AAD7C,gBAAO;AAAA,EAGP;AACF;AAGO,IAAM,oBAAN,cAAgC,MAAM;AAAA,EAE3C,YAAY,SAAiC,OAAiB;AAC5D,UAAM,OAAO;AAD8B;AAD7C,gBAAO;AAAA,EAGP;AACF;AAGO,IAAM,oBAAN,cAAgC,MAAM;AAAA,EAE3C,YAAY,SAAiC,OAAiB;AAC5D,UAAM,OAAO;AAD8B;AAD7C,gBAAO;AAAA,EAGP;AACF;AAGO,IAAM,0BAAN,cAAsC,MAAM;AAAA,EAEjD,YAAY,SAAiC,OAAiB;AAC5D,UAAM,OAAO;AAD8B;AAD7C,gBAAO;AAAA,EAGP;AACF;AAGO,IAAM,uBAAN,cAAmC,MAAM;AAAA,EAE9C,YAAY,SAAiC,OAAiB;AAC5D,UAAM,OAAO;AAD8B;AAD7C,gBAAO;AAAA,EAGP;AACF;AAEO,IAAM,sBAAN,cAAkC,MAAM;AAAA,EAE7C,YAAY,SAAiC,OAAiB;AAC5D,UAAM,OAAO;AAD8B;AAD7C,gBAAO;AAAA,EAGP;AACF;AAEO,IAAM,mBAAN,cAA+B,MAAM;AAAA,EAE1C,YAAY,SAAiC,OAAiB;AAC5D,UAAM,OAAO;AAD8B;AAD7C,gBAAO;AAAA,EAGP;AACF;AAEO,IAAM,yBAAN,cAAqC,MAAM;AAAA,EAEhD,YAAY,SAAiC,OAAiB;AAC5D,UAAM,OAAO;AAD8B;AAD7C,gBAAO;AAAA,EAGP;AACF;AAEO,IAAM,uBAAN,cAAmC,MAAM;AAAA,EAE9C,YAAY,SAAiC,OAAiB;AAC5D,UAAM,OAAO;AAD8B;AAD7C,gBAAO;AAAA,EAGP;AACF;AAEO,IAAM,oBAAN,cAAgC,MAAM;AAAA,EAE3C,YAAY,SAAiC,OAAiB;AAC5D,UAAM,OAAO;AAD8B;AAD7C,gBAAO;AAAA,EAGP;AACF;AAEO,IAAM,2BAAN,cAAuC,MAAM;AAAA,EAElD,YAAY,SAAiC,OAAiB;AAC5D,UAAM,OAAO;AAD8B;AAD7C,gBAAO;AAAA,EAGP;AACF;AAEO,SAAS,kBAAkB,MAAc,SAAiB;AAC/D,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO,IAAI,uBAAuB,OAAO;AAAA,IAC3C,KAAK;AACH,aAAO,IAAI,qBAAqB,OAAO;AAAA,IACzC;AACE,aAAO;AAAA,EACX;AACF;;;ACxFO,IAAM,iBAAiB;AAEvB,IAAM,mBAAmB;AAGzB,IAAM,yBAA0C;AAAA,EACrD,MAAM;AAAA,EACN,MAAM;AAAA,EACN,KAAK;AACP;AAGO,IAAM,0BAA4C;AAAA,EACvD,GAAG;AAAA,EACH,QAAQ;AAAA,EACR,UAAU;AACZ;AAsBO,IAAM,kBAAkB,CAAC,WAAgC;AAC9D,MAAI,QAAQ;AACV,YAAQ,OAAO,YAAY,GAAG;AAAA,MAC5B,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT;AACE,eAAO;AAAA,IACX;AAAA,EACF;AACA,SAAO;AACT;AAOO,IAAM,qCAAqC,CAAC,cAAyB;AAC1E,QAAM,UAA8B,OAAO,QAAQ,SAAS,EACzD;AAAA,IACC,CAAC,CAAC,OAAO,KAAK,MACZ,gBAAgB,SAAS,KAAK,KAAK,UAAU;AAAA,EACjD,EACC,IAAI,CAAC,CAAC,OAAO,KAAK,MAAM,CAAC,OAAO,MAAM,MAAM,CAAC;AAEhD,MAAI,QAAQ,WAAW,GAAG;AACxB,UAAM,IAAI;AAAA,MACR,mBAAmB,gBAAgB,KAAK,IAAI,CAAC;AAAA,IAC/C;AAAA,EACF;AAEA,QAAM,aAAa,QAChB,OAAO,CAAC,CAAC2C,IAAG,MAAM,MAAM,WAAW,CAAC,EACpC,IAAI,CAAC,CAAC,OAAOA,EAAC,MAAM,KAAK;AAC5B,MAAI,WAAW,SAAS,GAAG;AACzB,UAAM,IAAI;AAAA,MACR,oCAAoC,WAAW,KAAK,IAAI,CAAC;AAAA,IAC3D;AAAA,EACF;AAEA,MAAI,IAAI,IAAI,QAAQ,IAAI,CAAC,CAACA,IAAG,MAAM,MAAM,MAAM,CAAC,EAAE,OAAO,GAAG;AAC1D,UAAM,IAAI;AAAA,MACR,8BAA8B,QAC3B,IAAI,CAAC,CAAC,OAAOA,EAAC,MAAM,KAAK,EACzB,KAAK,IAAI,CAAC;AAAA,IACf;AAAA,EACF;AACF;AAEA,IAAM,qBAAqB,CAAC;AAAA,EAC1B;AAAA,EACA,YAAY;AACd,MAGM;AACJ,MAAI,CAAC,MAAM,QAAQ,UAAU,GAAG;AAC9B,UAAM,IAAI;AAAA,MACR,aAAa,SAAS,6BAA6B,OAAO,UAAU;AAAA,IACtE;AAAA,EACF;AAEA,MAAI,WAAW,WAAW,GAAG;AAC3B,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,MAAI,CAAC,WAAW,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,MAAW,OAAO,MAAM,QAAQ,CAAC,GAAG;AACzE,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,aAAW,QAAQ,CAAC,WAAW,MAAM;AACnC,QAAI,UAAU,WAAW,GAAG;AAC1B,YAAM,IAAI;AAAA,QACR,+FAA+F,CAAC;AAAA,MAClG;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAEA,IAAM,oBAAoB,CAAC;AAAA,EACzB;AAAA,EACA,WAAW;AAAA,EACX,YAAY;AACd,MAIM;AACJ,MAAI,CAAC,MAAM,QAAQ,SAAS,GAAG;AAC7B,UAAM,IAAI;AAAA,MACR,aAAa,SAAS,6BAA6B,OAAO,SAAS;AAAA,IACrE;AAAA,EACF;AAEA,MAAI,UAAU,WAAW,GAAG;AAC1B,UAAM,IAAI;AAAA,MACR,aAAa,SAAS;AAAA,IACxB;AAAA,EACF;AAEA,YAAU,QAAQ,CAAC,aAAa;AAC9B,QAAI,CAAC,YAAY,OAAO,aAAa,YAAY,CAAC,UAAU;AAC1D,YAAM,IAAI;AAAA,QACR,kDAAkD,OAAO,QAAQ;AAAA,MACnE;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAOO,IAAM,cAAc,CAAC,QAAkB;AAC5C,MAAI,CAAC,MAAM,QAAQ,GAAG,GAAG;AACvB,UAAM,IAAI;AAAA,MACR,0CAA0C,OAAO,GAAG;AAAA,IACtD;AAAA,EACF;AAEA,MAAI,IAAI,WAAW,GAAG;AACpB,UAAM,IAAI,iBAAiB,uCAAuC;AAAA,EACpE;AAEA,QAAM,aAAa,IAChB,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAkB,EACvC,OAAO,CAAC,CAAC,IAAIA,EAAC,MAAM,OAAO,OAAO,QAAQ,EAC1C,IAAI,CAAC,CAACA,IAAG,CAAC,MAAM,CAAC;AAEpB,MAAI,WAAW,SAAS,GAAG;AACzB,UAAM,IAAI;AAAA,MACR,2BAA2B,WAAW,KAAK,IAAI,CAAC;AAAA,IAClD;AAAA,EACF;AAEA,QAAM,OAAO,oBAAI,IAAI;AACrB,QAAM,aAAa,IAAI,OAAO,CAAC,OAAO;AACpC,QAAI,KAAK,IAAI,EAAE,GAAG;AAChB,aAAO;AAAA,IACT;AACA,SAAK,IAAI,EAAE;AAAA,EACb,CAAC;AACD,MAAI,UAAU;AACd,MAAI,WAAW,SAAS,KAAK,WAAW,UAAU,GAAG;AACnD,UAAM,IAAI,iBAAiB,GAAG,OAAO,IAAI,WAAW,KAAK,IAAI,CAAC,EAAE;AAAA,EAClE;AACA,MAAI,WAAW,SAAS,GAAG;AACzB,UAAM,IAAI;AAAA,MACR,GAAG,OAAO,IAAI,WAAW,MAAM,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,UAAU,WACtD,MAAM,WAAW,SAAS,CAAC,EAC3B,KAAK,IAAI,CAAC;AAAA,IACf;AAAA,EACF;AACF;AAOO,IAAM,mBAAmB,CAAC,aAAwB;AACvD,MAAI,CAAC,UAAU;AACb;AAAA,EACF;AAEA,MAAI,OAAO,KAAK,QAAQ,EAAE,WAAW,GAAG;AACtC,UAAM,IAAI,iBAAiB,mCAAmC;AAAA,EAChE;AAEA,MACE,CAAC,OAAO,OAAO,QAAQ,EAAE;AAAA,IACvB,CAAC,MACC,MAAM,QACN,MAAM,UACN,OAAO,MAAM,YACb,OAAO,MAAM,YACb,OAAO,MAAM;AAAA,EACjB,GACA;AACA,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,oBAAoB,CAAC,cAA0B;AACnD,MAAI,CAAC,MAAM,QAAQ,SAAS,GAAG;AAC7B,UAAM,IAAI;AAAA,MACR,8CAA8C,OAAO,SAAS;AAAA,IAChE;AAAA,EACF;AAEA,YAAU,QAAQ,CAAC,aAAa,iBAAiB,QAAQ,CAAC;AAC5D;AAWO,IAAM,wBAAwB,CAAC;AAAA,EACpC;AAAA,EACA,SAAS;AAAA,EACT,kBAAkB;AAAA,EAClB,iBAAiB;AACnB,MAKM;AACJ,MAAI,CAAC,UAAU,cAAc,CAAC,UAAU,aAAa,CAAC,QAAQ;AAC5D,UAAM,IAAI;AAAA,MACR,oBAAoB,eAAe,UAAU,cAAc;AAAA,IAC7D;AAAA,EACF;AAEA,MAAI,UAAU,YAAY;AACxB,uBAAmB;AAAA,MACjB,YAAY,UAAU;AAAA,MACtB,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AAEA,MAAI,UAAU,WAAW;AACvB,sBAAkB;AAAA,MAChB,WAAW,UAAU;AAAA,MACrB,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AAEA,MAAI,UAAU,WAAW;AACvB,sBAAkB,UAAU,SAAS;AAAA,EACvC;AACF;AAEO,IAAM,uBAAuB,CAAC,iBAAyB,iBAAyB;AACrF,MAAI,kBAAkB,cAAc;AAClC,UAAM,IAAI,iBAAiB,qBAAqB,eAAe,2BAA2B,YAAY,EAAE;AAAA,EAC1G;AACF;AAOO,IAAM,gBAAgB,CAAC,UAAiB;AAC7C,MAAI,OAAO,UAAU,UAAU;AAC7B,UAAM,IAAI,iBAAiB,yCAAyC;AAAA,EACtE;AAEA,MAAI,OAAO,KAAK,KAAK,EAAE,UAAU,GAAG;AAClC,UAAM,IAAI;AAAA,MACR,0DAA0D,OAAO,KAAK,KAAK,EAAE,MAC7E;AAAA,IACF;AAAA,EACF;AAEA,SAAO,QAAQ,KAAK,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC9C,QACE,QAAQ,UACR,QAAQ,SACR,QAAQ,SACR,QAAQ,UACR,CAAC,CAAC,UAAU,UAAU,WAAW,QAAQ,EAAE,SAAS,OAAO,KAAK,GAChE;AACA,YAAM,IAAI;AAAA,QACR,8FAA8F,KAAK;AAAA,MACrG;AAAA,IACF;AAEA,QAAI,QAAQ,UAAU,QAAQ,OAAO;AACnC,UAAI,OAAO,KAAK,KAAK,EAAE,UAAU,GAAG;AAClC,cAAM,IAAI;AAAA,UACR,uFAAuF,KAAK;AAAA,QAC9F;AAAA,MACF;AAEA,YAAM,QAAQ,CAACC,OAAa,cAAcA,EAAC,CAAC;AAC5C;AAAA,IACF;AAEA,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,OAAO,KAAK,KAAK,EAAE,UAAU,GAAG;AAClC,cAAM,IAAI;AAAA,UACR,8DAA8D,KAAK;AAAA,QACrE;AAAA,MACF;AAEA,YAAM,CAAC,UAAU,OAAO,IAAI,OAAO,QAAQ,KAAK,EAAE,CAAC;AAEnD,UACE,CAAC,OAAO,QAAQ,OAAO,MAAM,EAAE,SAAS,QAAQ,KAChD,OAAO,YAAY,UACnB;AACA,cAAM,IAAI;AAAA,UACR,6CAA6C,QAAQ,aAAa,OAAO,OAAO;AAAA,QAClF;AAAA,MACF;AAEA,UAAI,CAAC,OAAO,MAAM,EAAE,SAAS,QAAQ,KAAK,CAAC,MAAM,QAAQ,OAAO,GAAG;AACjE,cAAM,IAAI;AAAA,UACR,6CAA6C,QAAQ,aAAa,OAAO;AAAA,QAC3E;AAAA,MACF;AAEA,UACE,CAAC,CAAC,OAAO,QAAQ,OAAO,QAAQ,OAAO,OAAO,OAAO,MAAM,EAAE;AAAA,QAC3D;AAAA,MACF,GACA;AACA,cAAM,IAAI;AAAA,UACR,qFAAqF,QAAQ;AAAA,QAC/F;AAAA,MACF;AAEA,UACE,CAAC,CAAC,UAAU,UAAU,SAAS,EAAE,SAAS,OAAO,OAAO,KACxD,CAAC,MAAM,QAAQ,OAAO,GACtB;AACA,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,UACE,MAAM,QAAQ,OAAO,MACpB,QAAQ,WAAW,KAClB,CAAC,QAAQ,MAAM,CAAC,SAAS,OAAO,SAAS,OAAO,QAAQ,CAAC,CAAC,IAC5D;AACA,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAOO,IAAM,wBAAwB,CAAC,kBAAiC;AACrE,MAAI,OAAO,kBAAkB,UAAU;AACrC,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,MAAI,OAAO,KAAK,aAAa,EAAE,UAAU,GAAG;AAC1C,UAAM,IAAI;AAAA,MACR,kEAAkE,aAAa;AAAA,IACjF;AAAA,EACF;AAEA,QAAM,CAAC,UAAU,OAAO,IAAI,OAAO,QAAQ,aAAa,EAAE,CAAC;AAC3D,MACE,CAAC;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,EAAE,SAAS,QAAQ,GACnB;AACA,UAAM,IAAI;AAAA,MACR,8IAA8I,QAAQ;AAAA,IACxJ;AAAA,EACF;AAEA,MAAI,aAAa,UAAU,aAAa,OAAO;AAC7C,QAAI,CAAC,MAAM,QAAQ,OAAO,GAAG;AAC3B,YAAM,IAAI;AAAA,QACR,wBAAwB,QAAQ,yDAAyD,OAAO;AAAA,MAClG;AAAA,IACF;AAEA,QAAI,QAAQ,UAAU,GAAG;AACvB,YAAM,IAAI;AAAA,QACR,wCAAwC,QAAQ;AAAA,MAClD;AAAA,IACF;AAEA,YAAQ,QAAQ,CAAC,SAAS,sBAAsB,IAAI,CAAC;AAAA,EACvD;AAEA,OACG,YAAY,eACX,YAAY,mBACZ,YAAY,YACZ,YAAY,kBACb,OAAQ,aAAqB,YAAY,SAAS,WAAW,IAC9D;AACA,UAAM,IAAI;AAAA,MACR,wBAAwB,QAAQ,sCAAsC,OAAO;AAAA,IAC/E;AAAA,EACF;AACF;AASO,IAAM,kBAAkB,CAAC;AAAA,EAC9B;AAAA,EACA;AACF,MAGM;AACJ,MAAI,CAAC,MAAM,QAAQ,OAAO,GAAG;AAC3B,UAAM,IAAI,iBAAiB,4CAA4C;AAAA,EACzE;AAEA,QAAM,cAAc,OAAO,KAAK,WAAW;AAC3C,UAAQ,QAAQ,CAAC,SAAS;AACxB,QAAI,OAAQ,SAAiB,UAAU;AACrC,YAAM,IAAI,iBAAiB,wCAAwC;AAAA,IACrE;AAEA,QAAI,CAAC,YAAY,SAAS,IAAI,GAAG;AAC/B,YAAM,IAAI;AAAA,QACR,yCAAyC,YAAY;AAAA,UACnD;AAAA,QACF,CAAC,aAAa,IAAI;AAAA,MACpB;AAAA,IACF;AAEA,QAAI,SAAS,SAAS,IAAI,GAAG;AAC3B,YAAM,IAAI,iBAAiB,GAAG,IAAI,oCAAoC;AAAA,IACxE;AAAA,EACF,CAAC;AACH;AAOO,IAAM,mBAAmB,CAAC,aAAqB;AACpD,MAAI,OAAQ,aAAqB,UAAU;AACzC,UAAM,IAAI;AAAA,MACR,+CAA+C,OAAO,QAAQ;AAAA,IAChE;AAAA,EACF;AAEA,MAAI,YAAY,GAAG;AACjB,UAAM,IAAI,iBAAiB,6CAA6C;AAAA,EAC1E;AACF;AAEO,IAAM,sBAAsB,CAAC,SAAiB;AACnD,MAAI;AACF,UAAM,MAAM,IAAI,IAAI,IAAI;AAExB,UAAM,MAAM,IAAI,aAAa;AAC7B,UAAM,OAAO,IAAI;AACjB,UAAM,OAAO,IAAI;AAEjB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,MAAM,OAAO,IAAI;AAAA,IACnB;AAAA,EACF,QAAQ;AACN,UAAM,IAAI,iBAAiB,gBAAgB,IAAI,EAAE;AAAA,EACnD;AACF;AACA,IAAM,gBAAgB,CAAC,cAAqC;AAC1D,QAAM,SAAS,IAAI,YAAY,UAAU,SAAS,CAAC;AACnD,QAAM,OAAO,IAAI,aAAa,MAAM;AACpC,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,SAAK,CAAC,IAAI,UAAU,CAAC;AAAA,EACvB;AACA,SAAO;AACT;AAEO,IAAM,kCAAkC,CAAC,eAAuC;AACrF,MAAI,CAAC,YAAY;AACf,WAAO;AAAA,EACT;AAEA,SAAO,WAAW,IAAI,eAAa;AACjC,UAAM,SAAS,cAAc,SAAS;AAEtC,UAAM,aAAa,IAAI,WAAW,MAAM;AACxC,UAAM,eAAe,MAAM,KAAK,YAAY,UAAQ,OAAO,aAAa,IAAI,CAAC,EAAE,KAAK,EAAE;AACtF,WAAO,KAAK,YAAY;AAAA,EAC1B,CAAC;AACH;;;ACpgBO,IAAM,0BAA0B,oBAAI,IAGzC;AAQK,IAAM,4BAA4B,CACvC,MACA,OACG;AACH,MAAI,wBAAwB,IAAI,IAAI,GAAG;AACrC,UAAM,IAAI;AAAA,MACR,gCAAgC,IAAI;AAAA,IACtC;AAAA,EACF;AACA,0BAAwB,IAAI,MAAM,EAAE;AACtC;AAQO,IAAM,uBAAuB,OAClC,gBACA,aACG;AACH,MAAI,CAAC,UAAU;AACb,YAAQ;AAAA,MACN,4DAA4D,cAAc;AAAA,IAC5E;AACA,WAAO;AAAA,EACT;AAEA,MAAI,SAAS,SAAS,UAAU;AAC9B,YAAQ;AAAA,MACN,4DAA4D,cAAc;AAAA,IAC5E;AACA,WAAO;AAAA,EACT;AAEA,QAAM,OAAO,SAAS;AAEtB,QAAM,oBAAoB,wBAAwB,IAAI,IAAI;AAC1D,MAAI,CAAC,mBAAmB;AACtB,YAAQ;AAAA,MACN,cAAc,cAAc,yBAAyB,iBAAiB,kDAAkD,iBAAiB,iIAAiI,iBAAiB;AAAA,IAC7R;AACA,WAAO;AAAA,EACT;AAEA,MAAI,oBACF,SAAS,SAAS,UAAW,SAAS,SAAiC,CAAC;AAE1E,MAAI;AACF,QAAI,kBAAkB,iBAAiB;AACrC,aAAO,kBAAkB,gBAAgB,iBAAiB;AAAA,IAC5D;AAEA,YAAQ;AAAA,MACN,sBAAsB,IAAI;AAAA,IAC5B;AACA,WAAO;AAAA,EACT,SAAS,GAAG;AACV,YAAQ;AAAA,MACN,sBAAsB,IAAI,iCAAiC,iBAAiB,qFAAqF,CAAC;AAAA,IACpK;AACA,WAAO;AAAA,EACT;AACF;AAQO,IAAM,6BAA6B,CAAC;AAAA,EACzC;AAAA,EACA;AACF,MAGkD;AAChD,MAAI,qBAAqB,yBAAyB;AAChD,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,MAAI,CAAC,qBAAqB,CAAC,yBAAyB;AAClD,WAAO;AAAA,EACT;AAEA,QAAM,KAAK,qBAAqB;AAChC,MACE,CAAC,GAAG,aACJ,CAAC,GAAG,QACJ,CAAE,GAAG,YAAuC,iBAC5C;AACA,WAAO,EAAE,MAAM,SAAS;AAAA,EAC1B;AAEA,MAAI,GAAG,eAAgB,IAAG,eAAe,GAAG,UAAU,CAAC;AACvD,SAAO;AAAA,IACL,MAAM,GAAG;AAAA,IACT,MAAM;AAAA,IACN,QAAQ,GAAG,UAAU;AAAA,EACvB;AACF;AAQO,IAAM,qBACX,YAAqD;AACnD,MAAI;AACF,UAAM,EAAE,yBAAyB,IAAI,MAAM,OACzC,4BACF;AACA,QAAI,CAAC,wBAAwB,IAAI,IAAI,yBAAyB,EAAE,IAAI,GAAG;AACrE,gCAA0B,WAAW,wBAAwB;AAAA,IAC/D;AAAA,EACF,SAAS,GAAG;AACV,YAAQ,MAAM,CAAC;AACf,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ,CAAC;AAAA,EACX;AACF;;;ACrJK,IAAM,gCAAgC,OAAO;AAAA,EAClD;AAAA,EACA;AACF,MAGM;AACJ,MAAI,eAAe,QAAQ,eAAe,OAAO;AAC/C,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,QAAM,iCACJ,2BAA2B;AAAA,IACzB;AAAA,IACA,yBAAyB,eAAe;AAAA,EAC1C,CAAC,KAAM,MAAM,mBAAmB;AAElC,SAAO;AAAA,IACL,GAAI,iBAAiB,CAAC;AAAA,IACtB,oBAAoB;AAAA,EACtB;AACF;AAKO,IAAM,gCAAgC,OAAO;AAAA,EAClD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAQM;AACJ,MAAI,iBAAiB,QAAQ,OAAO,iBAAiB,SAAS,UAAU;AACtE,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,MAAI,iBAAiB,SAAS,OAAO,iBAAiB,UAAU,UAAU;AACxE,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,QAAM,oBACJ,4BACC,MAAM;AAAA,IACL;AAAA,IACA,qBAAqB,qBAAqB;AAAA,EAC5C;AAEF,QAAM,uBAAuB,iBAAiB;AAE9C,MACE,qBACA,kBAAkB,wBAClB,wBACA,qBAAqB,WACrB;AACA,sBAAkB,qBAAqB,qBAAqB,UAAU,CAAC;AAAA,EACzE;AAEA,SAAO;AAAA,IACL,qBAAqB;AAAA,MACnB,MAAM,iBAAiB;AAAA,MACvB,OAAO,iBAAiB;AAAA,MACxB,oBACE,wBACA,2BAA2B,EAAE,mBAAmB,qBAAqB,CAAC;AAAA,IAC1E;AAAA,IACA,yBAAyB;AAAA,EAC3B;AACF;;;ACuEO,IAAM,iBAAN,MAAM,gBAAqC;AAAA;AAAA;AAAA;AAAA;AAAA,EAahD,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAmB;AACjB,SAAK,eAAe;AACpB,SAAK,YAAY;AACjB,SAAK,KAAK;AACV,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,iBAAiB;AACtB,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EAEA,IAAW,OAAe;AACxB,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAY,KAAK,MAAc;AAC7B,SAAK,QAAQ;AAAA,EACf;AAAA,EAEA,IAAW,gBAAyC;AAClD,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAY,cAAc,eAAwC;AAChE,SAAK,iBAAiB;AAAA,EACxB;AAAA,EAEA,IAAW,WAA2C;AACpD,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAY,SAAS,UAA0C;AAC7D,SAAK,YAAY;AAAA,EACnB;AAAA,EAEA,IAAW,oBAAmD;AAC5D,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAc,kBACZ,mBACA;AACA,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EAEA,MAAgB,OAIb;AACD,UAAM,aAAa,MAAM,KAAK,aAAa,MAAM;AACjD,WAAO;AAAA,MACL,GAAG;AAAA,MACH,eAAe,KAAK;AAAA,IACtB;AAAA,EACF;AAAA,EAEA,MAAc,MAAM,WAA0C;AAC5D,QAAI,CAAC,KAAK,oBAAoB;AAC5B,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO,MAAM,KAAK,mBAAmB,SAAS,SAAS;AAAA,EACzD;AAAA,EAEA,MAAc,eAAe;AAAA,IAC3B;AAAA,IACA;AAAA,IACA,SAAS;AAAA,EACX,GAIG;AACD,uCAAmC,SAAS;AAC5C,gBAAY,UAAU,GAAG;AACzB,0BAAsB,EAAE,WAAW,OAAO,CAAC;AAC3C,yBAAqB,UAAU,IAAI,QAAQ,YAAY;AAEvD,QAAI,CAAC,UAAU,cAAc,UAAU,WAAW;AAChD,gBAAU,aAAa,MAAM,KAAK,MAAM,UAAU,SAAS;AAAA,IAC7D;AAAA,EACF;AAAA,EAEQ,YACN,SACA,KACA,OACA,eACA;AACA,oBAAgB,EAAE,SAAS,SAAS,CAAC,WAAW,EAAE,CAAC;AACnD,QAAI,IAAK,aAAY,GAAG;AACxB,QAAI,MAAO,eAAc,KAAK;AAC9B,QAAI,cAAe,uBAAsB,aAAa;AAAA,EACxD;AAAA,EAEA,MAAc,aACZ,WACA,SACA,KACA,OACA,eACA,UACyB;AACzB,0BAAsB;AAAA,MACpB;AAAA,MACA,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,IAClB,CAAC;AACD,oBAAgB,EAAE,QAAQ,CAAC;AAE3B,QAAI,IAAK,aAAY,GAAG;AACxB,QAAI,MAAO,eAAc,KAAK;AAC9B,QAAI,cAAe,uBAAsB,aAAa;AACtD,QAAI,SAAU,kBAAiB,QAAQ;AAEvC,QAAI;AACJ,QAAI,CAAC,UAAU,YAAY;AACzB,mBAAa,MAAM,KAAK,MAAM,UAAU,SAAU;AAAA,IACpD,OAAO;AACL,mBAAa,UAAU;AAAA,IACzB;AAEA,WAAO;AAAA,MACL,GAAG;AAAA,MACH;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEQ,eACN,KACA,OACA,eACA;AACA,QAAI,IAAK,aAAY,GAAG;AACxB,QAAI,MAAO,eAAc,KAAK;AAC9B,QAAI,cAAe,uBAAsB,aAAa;AAAA,EACxD;AAAA,EAEA,MAAa,QAAyB;AACpC,UAAM,EAAE,KAAK,IAAI,MAAM,eAAI,gBAAgB;AAAA,MACzC,QAAQ,KAAK;AAAA,MACb,MAAM,MAAM,KAAK,KAAK;AAAA,IACxB,CAAC;AAED,WAAO;AAAA,EACT;AAAA,EAEA,MAAa,IAAI;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAMG;AACD,UAAM,YAAuB;AAAA,MAC3B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,UAAM,eAAe,MAAM,KAAK,aAAa,gBAAgB;AAE7D,UAAM,KAAK,eAAe,EAAE,WAAW,aAAa,CAAC;AAErD,UAAM,yBAAyB,MAAM,KAAK,aAAa,uBAAuB;AAC9E,UAAM,mBAAmB,yBAAyB,gCAAgC,UAAU,UAAU,IAAI,UAAU;AAGpH,UAAM,eAAI,cAAc;AAAA,MACtB,QAAQ,KAAK;AAAA,MACb,MAAM,MAAM,KAAK,KAAK;AAAA,MACtB,MAAM;AAAA,QACJ,KAAK,UAAU;AAAA,QACf,YAAY;AAAA,QACZ,WAAW,UAAU;AAAA,QACrB,WAAW,UAAU;AAAA,QACrB,MAAM,UAAU;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,IACX,OAOK,CAAC,GACqB;AAC3B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU,CAAC,aAAa,WAAW;AAAA,IACrC,IAAI;AAEJ,SAAK,YAAY,SAAS,KAAK,OAAO,aAAa;AAEnD,UAAM,EAAE,KAAK,IAAI,MAAM,eAAI,cAAc;AAAA,MACvC,QAAQ,KAAK;AAAA,MACb,MAAM,MAAM,KAAK,KAAK;AAAA,MACtB,MAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,gBAAgB;AAAA,QAChB;AAAA,MACF;AAAA,IACF,CAAC;AAED,WAAO,IAAI,UAAiB;AAAA,MAC1B,WAAW,KAAK,aAAa,CAAC;AAAA,MAC9B,YAAY,KAAK,cAAc,CAAC;AAAA,MAChC,KAAK,KAAK;AAAA,MACV,SAAS,KAAK;AAAA,MACd,WAAY,KAAK,aAAa,CAAC;AAAA,MAC/B,MAAM,KAAK,QAAQ,CAAC;AAAA,IACtB,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,KAAK,EAAE,QAAQ,GAAG,GAA2C;AACxE,WAAO,KAAK,IAAI,EAAE,MAAM,CAAC;AAAA,EAC3B;AAAA,EAEA,MAAa,MAAyC;AAAA,IACpD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA,UAAU,CAAC,aAAa,aAAa,WAAW;AAAA,EAClD,GASgC;AAC9B,UAAM,YAA2B;AAAA,MAC/B,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,MAAM;AAAA,IACR;AAEA,UAAM,iBAAiB,MAAM,KAAK;AAAA,MAChC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,UAAM,EAAE,KAAK,IAAI,MAAM,eAAI,gBAAgB;AAAA,MACzC,QAAQ,KAAK;AAAA,MACb,MAAM,MAAM,KAAK,KAAK;AAAA,MACtB,MAAM;AAAA,QACJ,KAAK,eAAe;AAAA,QACpB;AAAA,QACA,WAAW;AAAA,QACX,kBAAkB,eAAe;AAAA,QACjC;AAAA,QACA,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAED,WAAO,IAAI,YAAY;AAAA,MACrB,WAAW,KAAK,aAAa,CAAC;AAAA,MAC9B,WAAW,KAAK,aAAa,CAAC;AAAA,MAC9B,YAAY,KAAK,cAAc,CAAC;AAAA,MAChC,KAAK,KAAK,OAAO,CAAC;AAAA,MAClB,SAAS,KAAK;AAAA,MACd,WAAY,KAAK,aAAa,CAAC;AAAA,MAC/B,MAAM,KAAK,QAAQ,CAAC;AAAA,IACtB,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,OAAO;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAIkB;AAChB,QAAI,KAAM,MAAK,OAAO;AAEtB,QAAI,UAAU;AACZ,uBAAiB,QAAQ;AACzB,WAAK,WAAW;AAAA,IAClB;AAEA,UAAM,EAAE,qBAAqB,wBAAwB,IAAI,gBACrD,MAAM,8BAA8B;AAAA,MACpC,gBAAgB,KAAK;AAAA,MACrB,sBAAsB,KAAK;AAAA,MAC3B,kBAAkB;AAAA,MAClB,0BAA0B,KAAK;AAAA,IACjC,CAAC,IACC,CAAC;AAEL,QAAI,yBAAyB;AAC3B,WAAK,oBAAoB;AAAA,IAC3B;AAEA,QAAI,qBAAqB;AACvB,WAAK,gBAAgB;AAAA,QACnB,MAAM,EAAE,GAAG,KAAK,cAAc,MAAM,GAAG,oBAAoB,KAAK;AAAA,QAChE,OAAO,EAAE,GAAG,KAAK,cAAc,OAAO,GAAG,oBAAoB,MAAM;AAAA,QACnE,mBAAmB,oBAAoB;AAAA,MACzC;AAAA,IACF;AAEA,UAAM,eAAI,iBAAiB;AAAA,MACzB,QAAQ,KAAK;AAAA,MACb,MAAM,MAAM,KAAK,KAAK;AAAA,MACtB,MAAM;AAAA,QACJ,UAAU;AAAA,QACV,cAAc;AAAA,QACd,mBAAmB;AAAA,MACrB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,KAAK,EAAE,KAAK,GAA0C;AACjE,UAAM,EAAE,KAAK,IAAI,MAAM,eAAI,eAAe;AAAA,MACxC,QAAQ,KAAK;AAAA,MACb,MAAM,MAAM,KAAK,KAAK;AAAA,MACtB,MAAM,EAAE,UAAU,KAAK;AAAA,IACzB,CAAC;AAED,WAAO,IAAI,gBAAe;AAAA,MACxB,cAAc,KAAK;AAAA,MACnB,WAAW,KAAK;AAAA,MAChB,MAAM,KAAK;AAAA,MACX,IAAI,KAAK;AAAA,MACT,mBAAmB,KAAK;AAAA,MACxB,UAAU,KAAK,YAAY;AAAA,MAC3B,eAAe,KAAK;AAAA,IACtB,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,OAAO;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAMkB;AAChB,UAAM,YAAuB;AAAA,MAC3B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,UAAM,eAAe,MAAM,KAAK,aAAa,gBAAgB;AAE7D,UAAM,KAAK,eAAe,EAAE,WAAW,cAAc,QAAQ,KAAK,CAAC;AAEnE,UAAM,yBAAyB,MAAM,KAAK,aAAa,uBAAuB;AAC9E,UAAM,mBAAmB,yBAAyB,gCAAgC,UAAU,UAAU,IAAI,UAAU;AAEpH,UAAM,eAAI,iBAAiB;AAAA,MACzB,QAAQ,KAAK;AAAA,MACb,MAAM,MAAM,KAAK,KAAK;AAAA,MACtB,MAAM;AAAA,QACJ,KAAK,UAAU;AAAA,QACf,YAAY;AAAA,QACZ,WAAW,UAAU;AAAA,QACrB,MAAM,UAAU;AAAA,QAChB,WAAW,UAAU;AAAA,MACvB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,OAAO;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAMkB;AAChB,UAAM,YAAuB;AAAA,MAC3B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,UAAM,eAAe,MAAM,KAAK,aAAa,gBAAgB;AAC7D,UAAM,KAAK,eAAe,EAAE,WAAW,cAAc,QAAQ,KAAK,CAAC;AAEnE,UAAM,yBAAyB,MAAM,KAAK,aAAa,uBAAuB;AAC9E,UAAM,mBAAmB,yBAAyB,gCAAgC,UAAU,UAAU,IAAI,UAAU;AAEpH,UAAM,eAAI,iBAAiB;AAAA,MACzB,QAAQ,KAAK;AAAA,MACb,MAAM,MAAM,KAAK,KAAK;AAAA,MACtB,MAAM;AAAA,QACJ,KAAK,UAAU;AAAA,QACf,YAAY;AAAA,QACZ,WAAW,UAAU;AAAA,QACrB,MAAM,UAAU;AAAA,QAChB,WAAW,UAAU;AAAA,MACvB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,OAAO;AAAA,IAClB;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAIkB;AAChB,SAAK,eAAe,KAAK,OAAO,aAAa;AAE7C,UAAM,eAAI,iBAAiB;AAAA,MACzB,QAAQ,KAAK;AAAA,MACb,MAAM,MAAM,KAAK,KAAK;AAAA,MACtB,MAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH;AACF;;;ACrrBO,SAAS,WAAW,iBAAsB,CAAC,GAAQ;AACxD,QAAM,0BAA0B,eAAe;AAE/C,QAAM,qBAAqB,CAAC,QAAa,YAAsB;AAC7D,QAAI,CAAC,MAAM,QAAQ,OAAO,SAAS,GAAG;AACpC,aAAO,YAAY,CAAC;AAAA,IACtB;AAEA,UAAM,iBAAiB,CAAC,6BAA6B,UAAU;AAC/D,eAAW,OAAO,gBAAgB;AAChC,UAAI,CAAC,OAAO,UAAU,SAAS,GAAG,GAAG;AACnC,eAAO,UAAU,KAAK,GAAG;AAAA,MAC3B;AAAA,IACF;AAEA,QAAI,OAAO,4BAA4B,YAAY;AACjD,aAAO,wBAAwB,QAAQ,OAAO;AAAA,IAChD;AACA,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL,GAAG;AAAA,IACH,SAAS;AAAA,EACX;AACF;;;ACfA,IAAM,eAAe,CAAC,UAAwB;AAC5C,SAAO;AAAA,KACJ,OAAO,SAAS,eAAe,OAAO,SAAS,kBAC7C,MAAM,SAAS,SAAS,cAAc,KACrC,MAAM,SAAS,SAAS,iBAAiB,KACzC,MAAM,SAAS,SAAS,WAAW;AAAA,EACzC;AACF;AAEO,IAAM,cAA4B,OAAO,OAAO,SAAS;AAC9D,MAAI;AACJ,MAAI;AACF,eAAW,MAAM,MAAM,OAAO,IAAI;AAAA,EACpC,SAAS,KAAK;AACZ,QAAI,aAAa,GAAG,GAAG;AACrB,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,UAAM,IAAI,sBAAsB,6BAA6B;AAAA,EAC/D;AAEA,MAAI,SAAS,IAAI;AACf,WAAO;AAAA,EACT;AAEA,UAAQ,SAAS,QAAQ;AAAA,IACvB,KAAK;AACH,UAAI,SAAS;AACb,UAAI;AACF,cAAM,eAAe,MAAM,SAAS,KAAK;AACzC,iBAAS,aAAa,WAAW;AAAA,MACnC,QAAQ;AAAA,MAAC;AACT,YAAM,IAAI;AAAA,QACR,kBACG,MAAkB,OAAO,QAC5B,iBAAiB,MAAM;AAAA,MACzB;AAAA,IACF,KAAK;AACH,YAAM,IAAI,wBAAwB,cAAc;AAAA,IAClD,KAAK;AACH,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF,KAAK;AACH,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF,KAAK;AACH,YAAM,IAAI,kBAAkB,6BAA6B;AAAA,IAC3D,KAAK;AACH,YAAM,OAAO,MAAM,SAAS,KAAK;AACjC,UAAI,QAAQ,MAAM,QAAQ,WAAW,gBAAgB,GAAG;AACtD,cAAM,IAAI,yBAAyB,MAAM,OAAO;AAAA,MAClD;AACA;AAAA,EACJ;AAEA,QAAM,IAAI;AAAA,IACR;AAAA,EACF;AACF;;;AClCO,IAAM,cAAN,MAAkB;AAAA;AAAA;AAAA;AAAA;AAAA,EAOvB,YAAY,MAAwB;AAClC,UAAM,EAAE,MAAM,MAAM,KAAK,SAAS,aAAa,IAC7C,QAAQ;AAEV,UAAM,UAAU,GAAG,MAAM,UAAU,MAAM,MAAM,IAAI,IAAI,IAAI;AAE3D,UAAM,gBAAgB;AAAA,MACpB,GAAG;AAAA,MACH,QAAQ,gBAAgB,cAAc,MAAM;AAAA,MAC5C;AAAA,MACA;AAAA,IACF;AAEA,SAAK,YAAY,EAAa,EAAa,aAAa,CAAC;AACzD,SAAK,UAAU,UAAU,EAAE,OAAO,YAAY,CAAC;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAa,eAAe;AAAA,IAC1B;AAAA,IACA;AAAA,EACF,GAGkB;AAChB,UAAM,eAAI,eAAe;AAAA,MACvB,QAAQ,KAAK;AAAA,MACb,MAAM,EAAE,OAAO;AAAA,MACf,MAAM,EAAE,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAa,YAAY;AAAA,IACvB;AAAA,IACA;AAAA,EACF,GAGsB;AACpB,UAAM,EAAE,KAAK,IAAI,MAAM,eAAI,YAAY;AAAA,MACrC,QAAQ,KAAK;AAAA,MACb,MAAM,EAAE,QAAQ,UAAU,KAAK;AAAA,IACjC,CAAC;AAED,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAa,eAAe;AAAA,IAC1B;AAAA,IACA;AAAA,EACF,GAGkB;AAChB,UAAM,eAAI,eAAe;AAAA,MACvB,QAAQ,KAAK;AAAA,MACb,MAAM,EAAE,QAAQ,UAAU,KAAK;AAAA,IACjC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAa,cAAc,MAA8C;AACvE,UAAM,EAAE,QAAQ,KAAK,SAAS,GAAG,OAAO,IAAI;AAC5C,UAAM,EAAE,KAAK,IAAI,MAAM,eAAI,cAAc;AAAA,MACvC,QAAQ,KAAK;AAAA,MACb,MAAM,EAAE,OAAO;AAAA,MACf,OAAO,EAAE,OAAO,OAAO;AAAA,IACzB,CAAC;AAED,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAa,aAAa,EAAE,KAAK,GAAoC;AACnE,UAAM,eAAI,aAAa;AAAA,MACrB,QAAQ,KAAK;AAAA,MACb,MAAM,EAAE,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAa,UAAU,EAAE,KAAK,GAAsC;AAClE,UAAM,EAAE,KAAK,IAAI,MAAM,eAAI,UAAU;AAAA,MACnC,QAAQ,KAAK;AAAA,MACb,MAAM,EAAE,aAAa,KAAK;AAAA,IAC5B,CAAC;AAED,WAAO,KAAK;AAAA,EACd;AACF;;;ACzJA,YAAY,aAAa;AAuClB,IAAM,eAAN,MAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,EAUxB,YAAY,OAAkC,CAAC,GAAG;AAChD,QAAI;AAAA,MACF,OAAO,wBAAY;AAAA,MACnB,OAAO,wBAAY;AAAA,MACnB,MAAM,wBAAY;AAAA,MAClB,SAAS,wBAAY;AAAA,MACrB,WAAW,wBAAY;AAAA,MACvB,UAAU,wBAAY;AAAA,MACtB,eAAe,wBAAY;AAAA,IAC7B,IAAI;AAEJ,QAAI,KAAK,MAAM;AACb,cAAQ;AAAA,QACN;AAAA,MACF;AACA,YAAM,aAAa,oBAAoB,KAAK,IAAI;AAChD,YAAM,WAAW;AACjB,aAAO,WAAW;AAClB,aAAO,WAAW;AAAA,IACpB;AAEA,QAAI,KAAK,MAAM;AACb,cAAQ;AAAA,QACN;AAAA,MACF;AACA,UAAI,CAAC,SAAS;AACZ,kBAAU,CAAC;AAAA,MACb;AACA,UACE,CAAC,QAAQ,gBAAgB,KACzB,KAAK,KAAK,oBAAoB,oBAC9B,KAAK,KAAK,aACV;AACA,gBAAQ,gBAAgB,IAAI,KAAK,KAAK;AAAA,MACxC;AAAA,IACF;AAEA,UAAM,UAAU,GAAG,MAAM,UAAU,MAAM,MAAM,IAAI,IAAI,IAAI;AAE3D,SAAK,UAAU,UAAkB,YAAI;AACrC,SAAK,YAAY,YAAoB,YAAI;AAEzC,UAAM,gBAAgB;AAAA,MACpB,GAAG;AAAA,MACH,QAAQ,gBAAgB,cAAc,MAAM;AAAA,MAC5C;AAAA,MACA;AAAA,IACF;AAEA,SAAK,YAAY,EAAa,EAAa,aAAa,CAAC;AACzD,SAAK,UAAU,UAAU,EAAE,OAAO,YAAY,CAAC;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAW,SAA6B;AACtC,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAc,OAAO,QAA4B;AAC/C,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAW,WAA+B;AACxC,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAc,SAAS,UAA8B;AACnD,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAW,kBAAiD;AAC1D,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAc,gBAAgB,iBAAgD;AAC5E,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA,EAGA,MAAa,QAAuD;AAClE,QAAI,CAAC,KAAK,WAAW,CAAC,KAAK,WAAW;AACpC,YAAM,EAAE,QAAQ,UAAU,IAAI,MAAM,KAAK,gBAAgB;AACzD,YAAM,YAAY,CAAC,GAAG,IAAI,IAAI,SAAS,CAAC;AACxC,WAAK,UAAU;AACf,UAAI,UAAU,WAAW,GAAG;AAC1B,cAAM,IAAI;AAAA,UACR,2DAA2D,KAAK,MAAM;AAAA,QACxE;AAAA,MACF;AACA,UAAI,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,KAAK;AAChD,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AACA,WAAK,YAAY,UAAU,CAAC;AAAA,IAC9B;AACA,WAAO,EAAE,QAAQ,KAAK,SAAS,UAAU,KAAK,UAAU;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAa,kBAAyC;AACpD,UAAM,EAAE,KAAK,IAAI,MAAM,eAAI,gBAAgB;AAAA,MACzC,QAAQ,KAAK;AAAA,IACf,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAa,YAA6B;AACxC,UAAM,EAAE,KAAK,IAAI,MAAM,eAAI,UAAU;AAAA,MACnC,QAAQ,KAAK;AAAA,IACf,CAAC;AACD,WAAO,KAAK,sBAAsB;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAa,gBACX,MAIuB;AACvB,UAAM,EAAE,QAAQ,KAAK,SAAS,EAAE,IAAI,QAAQ,CAAC;AAE7C,UAAM,EAAE,KAAK,IAAI,MAAM,eAAI,gBAAgB;AAAA,MACzC,QAAQ,KAAK;AAAA,MACb,MAAM,MAAM,KAAK,MAAM;AAAA,MACvB,OAAO,EAAE,OAAO,OAAO;AAAA,IACzB,CAAC;AAED,WAAO,QAAQ;AAAA,MACb,KAAK;AAAA,QACH,OAAO,eACL,IAAI,eAAe;AAAA,UACjB,cAAc;AAAA,UACd,WAAW,KAAK;AAAA,UAChB,MAAM,WAAW;AAAA,UACjB,IAAI,WAAW;AAAA,UACf,mBAAmB,MAAM;AAAA,YACvB,WAAW;AAAA,YACX,WAAW,mBAAmB,sBAAsB;AAAA,UACtD;AAAA,UACA,eAAe,WAAW;AAAA,UAC1B,UAAU,WAAW,YAAY;AAAA,QACnC,CAAC;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAa,mBAAoC;AAC/C,UAAM,EAAE,KAAK,IAAI,MAAM,eAAI,iBAAiB;AAAA,MAC1C,QAAQ,KAAK;AAAA,MACb,MAAM,MAAM,KAAK,MAAM;AAAA,IACzB,CAAC;AAED,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAa,iBAAiB;AAAA,IAC5B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAKwB;AACtB,UAAM,mBAAmB,MAAM,8BAA8B;AAAA,MAC3D;AAAA,MACA;AAAA,IACF,CAAC;AAED,UAAM,EAAE,KAAK,IAAI,MAAM,eAAI,iBAAiB;AAAA,MAC1C,QAAQ,KAAK;AAAA,MACb,MAAM,MAAM,KAAK,MAAM;AAAA,MACvB,MAAM;AAAA,QACJ;AAAA,QACA,eAAe;AAAA,QACf;AAAA,QACA,eAAe;AAAA,MACjB;AAAA,IACF,CAAC;AAED,WAAO,IAAI,eAAe;AAAA,MACxB,cAAc;AAAA,MACd,WAAW,KAAK;AAAA,MAChB;AAAA,MACA,eAAe,KAAK;AAAA,MACpB;AAAA,MACA,mBACE,qBACC,MAAM;AAAA,QACL,KAAK;AAAA,QACL,KAAK,mBAAmB,sBAAsB;AAAA,MAChD;AAAA,MACF,IAAI,KAAK;AAAA,IACX,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAa,cAAc;AAAA,IACzB;AAAA,IACA;AAAA,EACF,GAGwB;AACtB,UAAM,EAAE,KAAK,IAAI,MAAM,eAAI,cAAc;AAAA,MACvC,QAAQ,KAAK;AAAA,MACb,MAAM,EAAE,GAAI,MAAM,KAAK,MAAM,GAAI,eAAe,KAAK;AAAA,IACvD,CAAC;AAED,WAAO,IAAI,eAAe;AAAA,MACxB,cAAc;AAAA,MACd,WAAW,KAAK;AAAA,MAChB;AAAA,MACA,eAAe,KAAK;AAAA,MACpB,UAAU,KAAK,YAAY;AAAA,MAC3B,mBAAmB,oBACf,oBACA,MAAM;AAAA,QACN,KAAK;AAAA,QACL,KAAK,mBAAmB,sBAAsB;AAAA,MAChD;AAAA,MACF,IAAI,KAAK;AAAA,IACX,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAa,eACX,OACuB;AACvB,QAAI,MAAM,WAAW,EAAG,QAAO,CAAC;AAEhC,QAAI,uBAAuB;AAC3B,QAAI,OAAO,MAAM,CAAC,MAAM,UAAU;AAChC,6BAAwB,MAAmB,IAAI,CAAC,SAAS;AACvD,eAAO,EAAE,MAAM,MAAM,mBAAmB,OAAU;AAAA,MACpD,CAAC;AAAA,IACH;AAEA,QAAI,cAAc;AAKlB,WAAO,QAAQ;AAAA,MACb,YAAY,IAAI,OAAO,eAAe;AACpC,eAAO,KAAK,cAAc,EAAE,GAAG,WAAW,CAAC;AAAA,MAC7C,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAa,sBAAsB;AAAA,IACjC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAKwB;AACtB,UAAM,mBAAmB,MAAM,8BAA8B;AAAA,MAC3D;AAAA,MACA;AAAA,IACF,CAAC;AAED,UAAM,EAAE,KAAK,IAAI,MAAM,eAAI,iBAAiB;AAAA,MAC1C,QAAQ,KAAK;AAAA,MACb,MAAM,MAAM,KAAK,MAAM;AAAA,MACvB,MAAM;AAAA,QACJ;AAAA,QACA,eAAe;AAAA,QACf;AAAA,QACA,eAAe;AAAA,MACjB;AAAA,IACF,CAAC;AAED,WAAO,IAAI,eAAe;AAAA,MACxB,cAAc;AAAA,MACd,WAAW,KAAK;AAAA,MAChB;AAAA,MACA,eAAe,KAAK;AAAA,MACpB,UAAU,KAAK,YAAY;AAAA,MAC3B,mBACE,qBACC,MAAM;AAAA,QACL;AAAA,QACA,KAAK,mBAAmB,sBAAsB;AAAA,MAChD;AAAA,MACF,IAAI,KAAK;AAAA,IACX,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAa,iBAAiB,EAAE,KAAK,GAAoC;AACvE,UAAM,eAAI,iBAAiB;AAAA,MACzB,QAAQ,KAAK;AAAA,MACb,MAAM,EAAE,GAAI,MAAM,KAAK,MAAM,GAAI,eAAe,KAAK;AAAA,IACvD,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAa,QAAuB;AAClC,UAAM,eAAI,MAAM;AAAA,MACd,QAAQ,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAa,UAA2B;AACtC,UAAM,EAAE,KAAK,IAAI,MAAM,eAAI,QAAQ;AAAA,MACjC,QAAQ,KAAK;AAAA,IACf,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAa,qBAAiD;AAC5D,QAAI,CAAC,KAAK,iBAAiB;AACzB,YAAM,EAAE,KAAK,IAAI,MAAM,eAAI,gBAAgB;AAAA,QACzC,QAAQ,KAAK;AAAA,MACf,CAAC;AACD,WAAK,kBAAkB;AACvB,aAAO,KAAK;AAAA,IACd;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAa,kBAAmC;AAC9C,UAAM,kBAAkB,MAAM,KAAK,mBAAmB;AACtD,WAAO,gBAAgB,kBAAkB;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAa,yBAA2C;AACtD,UAAM,kBAAkB,MAAM,KAAK,mBAAmB;AACtD,WAAO,gBAAgB,4BAA4B;AAAA,EACrD;AACF;;;ACjeA,YAAYC,cAAa;AAQlB,IAAM,cAAN,cAA0B,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5C,YACE,OASK,CAAC,GACN;AACA,UAAM,SAAS,KAAK,UAAkB,aAAI;AAC1C,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,UAAM,SAAS,KAAK,UAAkB,aAAI;AAC1C,UAAM,WAAW,KAAK,YAAoB,aAAI;AAE9C,UAAM;AAAA,MACJ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA,SAAS,EAAE,kBAAkB,OAAO;AAAA,MACpC,cAAc,KAAK;AAAA,IACrB,CAAC;AAGD,SAAK,SAAS;AACd,SAAK,WAAW;AAAA,EAClB;AACF;AAMO,IAAM,mBAAN,cAA+B,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhD,YACE,OAKK,CAAC,GACN;AACA,UAAM,SAAS,KAAK,UAAkB,aAAI;AAC1C,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,UAAM;AAAA,MACJ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAK;AAAA,MACL,SAAS,EAAE,kBAAkB,OAAO;AAAA,MACpC,cAAc,KAAK;AAAA,IACrB,CAAC;AAAA,EACH;AACF;", "names": ["IncludeEnum", "q", "getAuthToken", "auth", "callback", "token", "getAuthToken", "jsonBodySerializer", "body", "key", "value", "getAuthToken", "separatorArrayExplode", "style", "separatorArrayNoExplode", "separatorObjectExplode", "serializeArrayParam", "allow<PERSON><PERSON><PERSON>d", "explode", "name", "value", "joinedValues", "v", "separator", "serializePrimitiveParam", "serializeObjectParam", "values", "key", "PATH_PARAM_RE", "defaultPathSerializer", "path", "_url", "url", "matches", "match", "h", "g", "c", "replaceValue", "createQuerySerializer", "array", "object", "queryParams", "search", "getParseAs", "contentType", "cleanContent", "type", "setAuthParams", "security", "options", "auth", "token", "x", "buildUrl", "getUrl", "baseUrl", "query", "querySerializer", "pathUrl", "mergeConfigs", "a", "b", "config", "mergeHeaders", "headers", "mergedHeaders", "header", "iterator", "Interceptors", "fn", "index", "createInterceptors", "defaultQuerySerializer", "defaultHeaders", "createConfig", "override", "z", "createClient", "_config", "getConfig", "setConfig", "interceptors", "request", "opts", "requestInit", "_fetch", "response", "result", "parseAs", "data", "error", "finalError", "_", "w", "process"]}