{"version": 3, "sources": ["../src/cli.ts", "../src/bindings.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport semver from \"semver\";\nimport binding from \"./bindings\";\n\ninterface NpmPackageData {\n  \"dist-tags\": {\n    latest: string;\n    [tag: string]: string;\n  };\n}\n\nconst getLatestVersion = async (packageName: string): Promise<string> => {\n  const response = await fetch(`https://registry.npmjs.org/${packageName}`);\n  if (!response.ok) {\n    throw new Error(`Failed to fetch package data: ${response.statusText}`);\n  }\n  const data: NpmPackageData = await response.json();\n  return data[\"dist-tags\"].latest;\n};\n\nconst update = async (): Promise<void> => {\n  try {\n    const installedVersion = process.env.CHROMADB_VERSION || \"0.0.0\";\n    const latestVersion = await getLatestVersion(\"chromadb\");\n\n    if (semver.lt(installedVersion, latestVersion)) {\n      console.log(`\\nA new chromadb version (${latestVersion}) is available!`);\n      console.log(\"\\n\\x1b[4mUpdat with npm\\x1b[0m\");\n      console.log(\"npm install chromadb@latest\");\n\n      console.log(\"\\n\\x1b[4mUpdat with pnpm\\x1b[0m\");\n      console.log(\"pnpm add chromadb@latest\");\n\n      console.log(\"\\n\\x1b[4mUpdat with yarn\\x1b[0m\");\n      console.log(\"yarn add chromadb@latest\");\n\n      console.log(\"\\n\\x1b[4mUpdat with bun\\x1b[0m\");\n      console.log(\"bun add chromadb@latest\\n\");\n    } else {\n      console.log(\n        `\\nYour chromadb version (${latestVersion}) is up-to-date!\\n`,\n      );\n    }\n  } catch (error) {\n    console.error(\"Error checking versions:\", error);\n  }\n};\n\nconst main = async () => {\n  const args: string[] = process.argv.slice(2);\n  if (args.length > 0 && args[0] === \"update\") {\n    await update();\n    return;\n  }\n\n  process.on(\"SIGINT\", () => {\n    process.exit(0);\n  });\n\n  binding.cli([\"chroma\", ...args]);\n};\n\nmain().finally();\n", "import { createRequire } from \"module\";\nimport os from \"os\";\n\nconst require = createRequire(import.meta.url);\nconst platform = os.platform();\nconst arch = os.arch();\n\nlet binding: any;\n\nif (platform === \"darwin\") {\n  if (arch === \"arm64\") {\n    binding = require(\"chromadb-js-bindings-darwin-arm64\");\n  } else if (arch === \"x64\") {\n    binding = require(\"chromadb-js-bindings-darwin-x64\");\n  } else {\n    throw new Error(`Unsupported architecture on macOS: ${arch}`);\n  }\n} else if (platform === \"linux\") {\n  if (arch === \"arm64\") {\n    binding = require(\"chromadb-js-bindings-linux-arm64-gnu\");\n  } else if (arch === \"x64\") {\n    binding = require(\"chromadb-js-bindings-linux-x64-gnu\");\n  } else {\n    throw new Error(`Unsupported architecture on Linux: ${arch}`);\n  }\n} else if (platform === \"win32\") {\n  if (arch === \"arm64\") {\n    binding = require(\"chromadb-js-bindings-win32-arm64-msvc\");\n  } else {\n    throw new Error(\n      `Unsupported Windows architecture: ${arch}. Only ARM64 is supported.`,\n    );\n  }\n} else {\n  throw new Error(`Unsupported platform: ${platform}`);\n}\n\nexport default binding;\n"], "mappings": ";;;;AACA,OAAO,YAAY;;;ACDnB,SAAS,qBAAqB;AAC9B,OAAO,QAAQ;AAEf,IAAMA,WAAU,cAAc,YAAY,GAAG;AAC7C,IAAM,WAAW,GAAG,SAAS;AAC7B,IAAM,OAAO,GAAG,KAAK;AAErB,IAAI;AAEJ,IAAI,aAAa,UAAU;AACzB,MAAI,SAAS,SAAS;AACpB,cAAUA,SAAQ,mCAAmC;AAAA,EACvD,WAAW,SAAS,OAAO;AACzB,cAAUA,SAAQ,iCAAiC;AAAA,EACrD,OAAO;AACL,UAAM,IAAI,MAAM,sCAAsC,IAAI,EAAE;AAAA,EAC9D;AACF,WAAW,aAAa,SAAS;AAC/B,MAAI,SAAS,SAAS;AACpB,cAAUA,SAAQ,sCAAsC;AAAA,EAC1D,WAAW,SAAS,OAAO;AACzB,cAAUA,SAAQ,oCAAoC;AAAA,EACxD,OAAO;AACL,UAAM,IAAI,MAAM,sCAAsC,IAAI,EAAE;AAAA,EAC9D;AACF,WAAW,aAAa,SAAS;AAC/B,MAAI,SAAS,SAAS;AACpB,cAAUA,SAAQ,uCAAuC;AAAA,EAC3D,OAAO;AACL,UAAM,IAAI;AAAA,MACR,qCAAqC,IAAI;AAAA,IAC3C;AAAA,EACF;AACF,OAAO;AACL,QAAM,IAAI,MAAM,yBAAyB,QAAQ,EAAE;AACrD;AAEA,IAAO,mBAAQ;;;AD1Bf,IAAM,mBAAmB,OAAO,gBAAyC;AACvE,QAAM,WAAW,MAAM,MAAM,8BAA8B,WAAW,EAAE;AACxE,MAAI,CAAC,SAAS,IAAI;AAChB,UAAM,IAAI,MAAM,iCAAiC,SAAS,UAAU,EAAE;AAAA,EACxE;AACA,QAAM,OAAuB,MAAM,SAAS,KAAK;AACjD,SAAO,KAAK,WAAW,EAAE;AAC3B;AAEA,IAAM,SAAS,YAA2B;AACxC,MAAI;AACF,UAAM,mBAAmB,QAAQ,IAAI,oBAAoB;AACzD,UAAM,gBAAgB,MAAM,iBAAiB,UAAU;AAEvD,QAAI,OAAO,GAAG,kBAAkB,aAAa,GAAG;AAC9C,cAAQ,IAAI;AAAA,0BAA6B,aAAa,iBAAiB;AACvE,cAAQ,IAAI,gCAAgC;AAC5C,cAAQ,IAAI,6BAA6B;AAEzC,cAAQ,IAAI,iCAAiC;AAC7C,cAAQ,IAAI,0BAA0B;AAEtC,cAAQ,IAAI,iCAAiC;AAC7C,cAAQ,IAAI,0BAA0B;AAEtC,cAAQ,IAAI,gCAAgC;AAC5C,cAAQ,IAAI,2BAA2B;AAAA,IACzC,OAAO;AACL,cAAQ;AAAA,QACN;AAAA,yBAA4B,aAAa;AAAA;AAAA,MAC3C;AAAA,IACF;AAAA,EACF,SAAS,OAAO;AACd,YAAQ,MAAM,4BAA4B,KAAK;AAAA,EACjD;AACF;AAEA,IAAM,OAAO,YAAY;AACvB,QAAM,OAAiB,QAAQ,KAAK,MAAM,CAAC;AAC3C,MAAI,KAAK,SAAS,KAAK,KAAK,CAAC,MAAM,UAAU;AAC3C,UAAM,OAAO;AACb;AAAA,EACF;AAEA,UAAQ,GAAG,UAAU,MAAM;AACzB,YAAQ,KAAK,CAAC;AAAA,EAChB,CAAC;AAED,mBAAQ,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACjC;AAEA,KAAK,EAAE,QAAQ;", "names": ["require"]}