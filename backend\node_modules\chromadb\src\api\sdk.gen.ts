// This file is auto-generated by @hey-api/openapi-ts

import type { Options as ClientOptions, TDataShape, Client } from '@hey-api/client-fetch';
import type { GetUserIdentityData, GetUserIdentityResponse2, GetUserIdentityError, HealthcheckData, HealthcheckResponse, HealthcheckError, HeartbeatData, HeartbeatResponse2, HeartbeatError, PreFlightChecksData, PreFlightChecksResponse, PreFlightChecksError, ResetData, ResetResponse, ResetError, CreateTenantData, CreateTenantResponse2, CreateTenantError, GetTenantData, GetTenantResponse2, GetTenantError, ListDatabasesData, ListDatabasesResponse, ListDatabasesError, CreateDatabaseData, CreateDatabaseResponse2, CreateDatabaseError, DeleteDatabaseData, DeleteDatabaseResponse2, DeleteDatabaseError, GetDatabaseData, GetDatabaseResponse, GetDatabaseError, ListCollectionsData, ListCollectionsResponse, ListCollectionsError, CreateCollectionData, CreateCollectionResponse, CreateCollectionError, DeleteCollectionData, DeleteCollectionResponse, DeleteCollectionError, GetCollectionData, GetCollectionResponse, GetCollectionError, UpdateCollectionData, UpdateCollectionResponse2, UpdateCollectionError, CollectionAddData, CollectionAddResponse, CollectionCountData, CollectionCountResponse, CollectionCountError, CollectionDeleteData, CollectionDeleteResponse, CollectionDeleteError, ForkCollectionData, ForkCollectionResponse, ForkCollectionError, CollectionGetData, CollectionGetResponse, CollectionGetError, CollectionQueryData, CollectionQueryResponse, CollectionQueryError, CollectionUpdateData, CollectionUpdateResponse, CollectionUpsertData, CollectionUpsertResponse, CollectionUpsertError, CountCollectionsData, CountCollectionsResponse, CountCollectionsError, VersionData, VersionResponse } from './types.gen';
import { client as _heyApiClient } from './client.gen';

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {
    /**
     * You can provide a client instance returned by `createClient()` instead of
     * individual options. This might be also useful if you want to implement a
     * custom client.
     */
    client?: Client;
    /**
     * You can pass arbitrary values through the `meta` object. This can be
     * used to access values that aren't defined as part of the SDK function.
     */
    meta?: Record<string, unknown>;
};

export class DefaultService {
    /**
     * Retrieves the current user's identity, tenant, and databases.
     */
    public static getUserIdentity<ThrowOnError extends boolean = true>(options?: Options<GetUserIdentityData, ThrowOnError>) {
        return (options?.client ?? _heyApiClient).get<GetUserIdentityResponse2, GetUserIdentityError, ThrowOnError>({
            url: '/api/v2/auth/identity',
            ...options
        });
    }
    
    /**
     * Health check endpoint that returns 200 if the server and executor are ready
     */
    public static healthcheck<ThrowOnError extends boolean = true>(options?: Options<HealthcheckData, ThrowOnError>) {
        return (options?.client ?? _heyApiClient).get<HealthcheckResponse, HealthcheckError, ThrowOnError>({
            url: '/api/v2/healthcheck',
            ...options
        });
    }
    
    /**
     * Heartbeat endpoint that returns a nanosecond timestamp of the current time.
     */
    public static heartbeat<ThrowOnError extends boolean = true>(options?: Options<HeartbeatData, ThrowOnError>) {
        return (options?.client ?? _heyApiClient).get<HeartbeatResponse2, HeartbeatError, ThrowOnError>({
            url: '/api/v2/heartbeat',
            ...options
        });
    }
    
    /**
     * Pre-flight checks endpoint reporting basic readiness info.
     */
    public static preFlightChecks<ThrowOnError extends boolean = true>(options?: Options<PreFlightChecksData, ThrowOnError>) {
        return (options?.client ?? _heyApiClient).get<PreFlightChecksResponse, PreFlightChecksError, ThrowOnError>({
            url: '/api/v2/pre-flight-checks',
            ...options
        });
    }
    
    /**
     * Reset endpoint allowing authorized users to reset the database.
     */
    public static reset<ThrowOnError extends boolean = true>(options?: Options<ResetData, ThrowOnError>) {
        return (options?.client ?? _heyApiClient).post<ResetResponse, ResetError, ThrowOnError>({
            url: '/api/v2/reset',
            ...options
        });
    }
    
    /**
     * Creates a new tenant.
     */
    public static createTenant<ThrowOnError extends boolean = true>(options: Options<CreateTenantData, ThrowOnError>) {
        return (options.client ?? _heyApiClient).post<CreateTenantResponse2, CreateTenantError, ThrowOnError>({
            url: '/api/v2/tenants',
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options?.headers
            }
        });
    }
    
    /**
     * Returns an existing tenant by name.
     */
    public static getTenant<ThrowOnError extends boolean = true>(options: Options<GetTenantData, ThrowOnError>) {
        return (options.client ?? _heyApiClient).get<GetTenantResponse2, GetTenantError, ThrowOnError>({
            url: '/api/v2/tenants/{tenant_name}',
            ...options
        });
    }
    
    /**
     * Lists all databases for a given tenant.
     */
    public static listDatabases<ThrowOnError extends boolean = true>(options: Options<ListDatabasesData, ThrowOnError>) {
        return (options.client ?? _heyApiClient).get<ListDatabasesResponse, ListDatabasesError, ThrowOnError>({
            url: '/api/v2/tenants/{tenant}/databases',
            ...options
        });
    }
    
    /**
     * Creates a new database for a given tenant.
     */
    public static createDatabase<ThrowOnError extends boolean = true>(options: Options<CreateDatabaseData, ThrowOnError>) {
        return (options.client ?? _heyApiClient).post<CreateDatabaseResponse2, CreateDatabaseError, ThrowOnError>({
            url: '/api/v2/tenants/{tenant}/databases',
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options?.headers
            }
        });
    }
    
    /**
     * Deletes a specific database.
     */
    public static deleteDatabase<ThrowOnError extends boolean = true>(options: Options<DeleteDatabaseData, ThrowOnError>) {
        return (options.client ?? _heyApiClient).delete<DeleteDatabaseResponse2, DeleteDatabaseError, ThrowOnError>({
            url: '/api/v2/tenants/{tenant}/databases/{database}',
            ...options
        });
    }
    
    /**
     * Retrieves a specific database by name.
     */
    public static getDatabase<ThrowOnError extends boolean = true>(options: Options<GetDatabaseData, ThrowOnError>) {
        return (options.client ?? _heyApiClient).get<GetDatabaseResponse, GetDatabaseError, ThrowOnError>({
            url: '/api/v2/tenants/{tenant}/databases/{database}',
            ...options
        });
    }
    
    /**
     * Lists all collections in the specified database.
     */
    public static listCollections<ThrowOnError extends boolean = true>(options: Options<ListCollectionsData, ThrowOnError>) {
        return (options.client ?? _heyApiClient).get<ListCollectionsResponse, ListCollectionsError, ThrowOnError>({
            url: '/api/v2/tenants/{tenant}/databases/{database}/collections',
            ...options
        });
    }
    
    /**
     * Creates a new collection under the specified database.
     */
    public static createCollection<ThrowOnError extends boolean = true>(options: Options<CreateCollectionData, ThrowOnError>) {
        return (options.client ?? _heyApiClient).post<CreateCollectionResponse, CreateCollectionError, ThrowOnError>({
            url: '/api/v2/tenants/{tenant}/databases/{database}/collections',
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options?.headers
            }
        });
    }
    
    /**
     * Deletes a collection in a given database.
     */
    public static deleteCollection<ThrowOnError extends boolean = true>(options: Options<DeleteCollectionData, ThrowOnError>) {
        return (options.client ?? _heyApiClient).delete<DeleteCollectionResponse, DeleteCollectionError, ThrowOnError>({
            url: '/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}',
            ...options
        });
    }
    
    /**
     * Retrieves a collection by ID or name.
     */
    public static getCollection<ThrowOnError extends boolean = true>(options: Options<GetCollectionData, ThrowOnError>) {
        return (options.client ?? _heyApiClient).get<GetCollectionResponse, GetCollectionError, ThrowOnError>({
            url: '/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}',
            ...options
        });
    }
    
    /**
     * Updates an existing collection's name or metadata.
     */
    public static updateCollection<ThrowOnError extends boolean = true>(options: Options<UpdateCollectionData, ThrowOnError>) {
        return (options.client ?? _heyApiClient).put<UpdateCollectionResponse2, UpdateCollectionError, ThrowOnError>({
            url: '/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}',
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options?.headers
            }
        });
    }
    
    /**
     * Adds records to a collection.
     */
    public static collectionAdd<ThrowOnError extends boolean = true>(options: Options<CollectionAddData, ThrowOnError>) {
        return (options.client ?? _heyApiClient).post<CollectionAddResponse, unknown, ThrowOnError>({
            url: '/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/add',
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options?.headers
            }
        });
    }
    
    /**
     * Retrieves the number of records in a collection.
     */
    public static collectionCount<ThrowOnError extends boolean = true>(options: Options<CollectionCountData, ThrowOnError>) {
        return (options.client ?? _heyApiClient).get<CollectionCountResponse, CollectionCountError, ThrowOnError>({
            url: '/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/count',
            ...options
        });
    }
    
    /**
     * Deletes records in a collection. Can filter by IDs or metadata.
     */
    public static collectionDelete<ThrowOnError extends boolean = true>(options: Options<CollectionDeleteData, ThrowOnError>) {
        return (options.client ?? _heyApiClient).post<CollectionDeleteResponse, CollectionDeleteError, ThrowOnError>({
            url: '/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/delete',
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options?.headers
            }
        });
    }
    
    /**
     * Forks an existing collection.
     */
    public static forkCollection<ThrowOnError extends boolean = true>(options: Options<ForkCollectionData, ThrowOnError>) {
        return (options.client ?? _heyApiClient).post<ForkCollectionResponse, ForkCollectionError, ThrowOnError>({
            url: '/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/fork',
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options?.headers
            }
        });
    }
    
    /**
     * Retrieves records from a collection by ID or metadata filter.
     */
    public static collectionGet<ThrowOnError extends boolean = true>(options: Options<CollectionGetData, ThrowOnError>) {
        return (options.client ?? _heyApiClient).post<CollectionGetResponse, CollectionGetError, ThrowOnError>({
            url: '/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/get',
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options?.headers
            }
        });
    }
    
    /**
     * Query a collection in a variety of ways, including vector search, metadata filtering, and full-text search
     */
    public static collectionQuery<ThrowOnError extends boolean = true>(options: Options<CollectionQueryData, ThrowOnError>) {
        return (options.client ?? _heyApiClient).post<CollectionQueryResponse, CollectionQueryError, ThrowOnError>({
            url: '/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/query',
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options?.headers
            }
        });
    }
    
    /**
     * Updates records in a collection by ID.
     */
    public static collectionUpdate<ThrowOnError extends boolean = true>(options: Options<CollectionUpdateData, ThrowOnError>) {
        return (options.client ?? _heyApiClient).post<CollectionUpdateResponse, unknown, ThrowOnError>({
            url: '/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/update',
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options?.headers
            }
        });
    }
    
    /**
     * Upserts records in a collection (create if not exists, otherwise update).
     */
    public static collectionUpsert<ThrowOnError extends boolean = true>(options: Options<CollectionUpsertData, ThrowOnError>) {
        return (options.client ?? _heyApiClient).post<CollectionUpsertResponse, CollectionUpsertError, ThrowOnError>({
            url: '/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/upsert',
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options?.headers
            }
        });
    }
    
    /**
     * Retrieves the total number of collections in a given database.
     */
    public static countCollections<ThrowOnError extends boolean = true>(options: Options<CountCollectionsData, ThrowOnError>) {
        return (options.client ?? _heyApiClient).get<CountCollectionsResponse, CountCollectionsError, ThrowOnError>({
            url: '/api/v2/tenants/{tenant}/databases/{database}/collections_count',
            ...options
        });
    }
    
    /**
     * Returns the version of the server.
     */
    public static version<ThrowOnError extends boolean = true>(options?: Options<VersionData, ThrowOnError>) {
        return (options?.client ?? _heyApiClient).get<VersionResponse, unknown, ThrowOnError>({
            url: '/api/v2/version',
            ...options
        });
    }
    
}