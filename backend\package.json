{"name": "dms-whisper-backend", "version": "1.0.0", "description": "Backend server for DM's <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> DM Assistant <PERSON><PERSON>", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["daggerheart", "dm", "assistant", "voice", "ai"], "author": "", "license": "ISC", "dependencies": {"@deepgram/sdk": "^4.4.0", "chromadb": "^3.0.4", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "openai": "^5.5.1", "socket.io": "^4.8.1"}, "devDependencies": {"nodemon": "^3.1.10"}}