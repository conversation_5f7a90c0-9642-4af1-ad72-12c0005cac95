const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
require('dotenv').config();

const SocketHandler = require('./src/services/SocketHandler');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:5173",
    methods: ["GET", "POST"],
    credentials: true
  }
});

// Middleware
app.use(cors({
  origin: process.env.FRONTEND_URL || "http://localhost:5173",
  credentials: true
}));
app.use(express.json());

// Routes
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'DM\'s Whisper Backend is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

app.get('/api/status', (req, res) => {
  res.json({
    deepgram: !!process.env.DEEPGRAM_API_KEY && process.env.DEEPGRAM_API_KEY !== '****************************************',
    openai: !!process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== '********************************************************************************************************************************************************************',
    services: 'operational'
  });
});

// Initialize socket handler
const socketHandler = new SocketHandler(io);

// Socket.IO connection handling
io.on('connection', (socket) => {
  socketHandler.handleConnection(socket);
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully');
  await socketHandler.cleanup();
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', async () => {
  console.log('SIGINT received, shutting down gracefully');
  await socketHandler.cleanup();
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(`DM's Whisper Backend running on port ${PORT}`);
  console.log(`Health check available at http://localhost:${PORT}/health`);
});
