/**
 * Data models and validation schemas for DM's Whisper entities
 */

class CharacterModel {
  constructor(data = {}) {
    this.name = data.name || null;
    this.race = data.race || null;
    this.class = data.class || null;
    this.appearance = {
      size: data.appearance?.size || null,
      description: data.appearance?.description || '',
      notable_features: data.appearance?.notable_features || []
    };
    this.personality = {
      traits: data.personality?.traits || [],
      demeanor: data.personality?.demeanor || null
    };
    this.abilities = {
      suggested_stats: data.abilities?.suggested_stats || null,
      special_abilities: data.abilities?.special_abilities || []
    };
    this.background = {
      occupation: data.background?.occupation || null,
      origin: data.background?.origin || null
    };
    this.equipment = data.equipment || [];
    this.notes = data.notes || '';
    this.imageUrl = data.imageUrl || null;
    this.createdAt = data.createdAt || new Date().toISOString();
    this.updatedAt = new Date().toISOString();
  }

  validate() {
    const errors = [];
    
    if (this.appearance.size && !['tiny', 'small', 'medium', 'large', 'huge'].includes(this.appearance.size.toLowerCase())) {
      errors.push('Invalid size value');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  toJSON() {
    return {
      name: this.name,
      race: this.race,
      class: this.class,
      appearance: this.appearance,
      personality: this.personality,
      abilities: this.abilities,
      background: this.background,
      equipment: this.equipment,
      notes: this.notes,
      imageUrl: this.imageUrl,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

class LocationModel {
  constructor(data = {}) {
    this.name = data.name || null;
    this.type = data.type || null;
    this.description = data.description || '';
    this.atmosphere = data.atmosphere || '';
    this.features = {
      notable_landmarks: data.features?.notable_landmarks || [],
      hazards: data.features?.hazards || [],
      resources: data.features?.resources || []
    };
    this.inhabitants = data.inhabitants || [];
    this.connections = data.connections || [];
    this.secrets = data.secrets || [];
    this.notes = data.notes || '';
    this.imageUrl = data.imageUrl || null;
    this.createdAt = data.createdAt || new Date().toISOString();
    this.updatedAt = new Date().toISOString();
  }

  validate() {
    const errors = [];
    
    const validTypes = ['tavern', 'dungeon', 'forest', 'city', 'village', 'castle', 'temple', 'cave', 'mountain', 'river', 'road'];
    if (this.type && !validTypes.includes(this.type.toLowerCase())) {
      // Allow custom types, just log for reference
      console.log('Custom location type:', this.type);
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  toJSON() {
    return {
      name: this.name,
      type: this.type,
      description: this.description,
      atmosphere: this.atmosphere,
      features: this.features,
      inhabitants: this.inhabitants,
      connections: this.connections,
      secrets: this.secrets,
      notes: this.notes,
      imageUrl: this.imageUrl,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

class ObjectModel {
  constructor(data = {}) {
    this.name = data.name || null;
    this.type = data.type || null;
    this.description = data.description || '';
    this.properties = {
      material: data.properties?.material || null,
      size: data.properties?.size || null,
      weight: data.properties?.weight || null,
      condition: data.properties?.condition || null
    };
    this.mechanical_effects = {
      damage: data.mechanical_effects?.damage || null,
      armor_value: data.mechanical_effects?.armor_value || null,
      special_properties: data.mechanical_effects?.special_properties || []
    };
    this.value = {
      estimated_cost: data.value?.estimated_cost || null,
      rarity: data.value?.rarity || null
    };
    this.history = data.history || '';
    this.notes = data.notes || '';
    this.imageUrl = data.imageUrl || null;
    this.createdAt = data.createdAt || new Date().toISOString();
    this.updatedAt = new Date().toISOString();
  }

  validate() {
    const errors = [];
    
    const validTypes = ['weapon', 'armor', 'tool', 'treasure', 'consumable', 'magical', 'mundane'];
    if (this.type && !validTypes.includes(this.type.toLowerCase())) {
      // Allow custom types
      console.log('Custom object type:', this.type);
    }
    
    const validRarities = ['common', 'uncommon', 'rare', 'very rare', 'legendary', 'artifact'];
    if (this.value.rarity && !validRarities.includes(this.value.rarity.toLowerCase())) {
      errors.push('Invalid rarity value');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  toJSON() {
    return {
      name: this.name,
      type: this.type,
      description: this.description,
      properties: this.properties,
      mechanical_effects: this.mechanical_effects,
      value: this.value,
      history: this.history,
      notes: this.notes,
      imageUrl: this.imageUrl,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

/**
 * Factory function to create appropriate model based on type
 */
function createEntityModel(type, data) {
  switch (type.toLowerCase()) {
    case 'character':
      return new CharacterModel(data);
    case 'location':
      return new LocationModel(data);
    case 'object':
      return new ObjectModel(data);
    default:
      throw new Error(`Unknown entity type: ${type}`);
  }
}

module.exports = {
  CharacterModel,
  LocationModel,
  ObjectModel,
  createEntityModel
};
