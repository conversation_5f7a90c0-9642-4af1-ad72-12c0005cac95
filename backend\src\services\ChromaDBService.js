const { ChromaApi } = require('chromadb');

class ChromaDBService {
  constructor() {
    this.client = null;
    this.collection = null;
    this.isEnabled = process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'placeholder_openai_key';

    if (!this.isEnabled) {
      console.warn('ChromaDB service disabled - OpenAI API key not configured');
    }
  }

  /**
   * Initialize ChromaDB client and collection
   */
  async initialize() {
    if (!this.isEnabled) {
      console.log('ChromaDB disabled - using mock data for development');
      return;
    }

    try {
      this.client = new ChromaApi({
        path: process.env.CHROMADB_PATH || 'http://localhost:8000'
      });

      // Create or get the Daggerheart knowledge collection
      this.collection = await this.client.getOrCreateCollection({
        name: 'daggerheart_knowledge'
      });

      console.log('ChromaDB initialized successfully');

      // Check if we need to populate the collection with initial data
      const count = await this.collection.count();
      if (count === 0) {
        await this.populateInitialData();
      }

    } catch (error) {
      console.error('Failed to initialize ChromaDB:', error);
      // Don't throw error in development mode
      if (process.env.NODE_ENV === 'development') {
        console.log('Continuing in development mode without ChromaDB');
        this.isEnabled = false;
      } else {
        throw error;
      }
    }
  }

  /**
   * Populate the collection with initial Daggerheart knowledge
   */
  async populateInitialData() {
    const daggerheartKnowledge = [
      {
        id: 'races_overview',
        content: 'Daggerheart features various ancestries including Human, Elf, Dwarf, Halfling, Orc, Goblin, Giant, Faerie, and more. Each ancestry has unique traits and cultural backgrounds.',
        metadata: { category: 'races', type: 'overview' }
      },
      {
        id: 'classes_overview', 
        content: 'Daggerheart classes include Guardian (tank), Seraph (healer), Wizard (arcane caster), Ranger (nature warrior), Rogue (stealth specialist), Bard (support), Sorcerer (chaotic magic), Warrior (martial fighter), and more.',
        metadata: { category: 'classes', type: 'overview' }
      },
      {
        id: 'magic_system',
        content: 'Magic in Daggerheart uses spell slots and focuses on narrative impact. Spells have different domains like Arcane, Divine, Primal, and Occult.',
        metadata: { category: 'magic', type: 'system' }
      },
      {
        id: 'combat_basics',
        content: 'Combat uses action tokens, with players spending tokens to take actions. Initiative is handled through narrative flow rather than strict turn order.',
        metadata: { category: 'combat', type: 'basics' }
      },
      {
        id: 'character_creation',
        content: 'Characters are created by choosing ancestry, class, community, and background. Stats are determined through point buy or rolling.',
        metadata: { category: 'character', type: 'creation' }
      }
    ];

    try {
      await this.collection.add({
        ids: daggerheartKnowledge.map(item => item.id),
        documents: daggerheartKnowledge.map(item => item.content),
        metadatas: daggerheartKnowledge.map(item => item.metadata)
      });

      console.log('Initial Daggerheart knowledge populated');
    } catch (error) {
      console.error('Failed to populate initial data:', error);
    }
  }

  /**
   * Query the knowledge base for relevant context
   * @param {string} query - The query text
   * @param {number} nResults - Number of results to return
   * @returns {Array} Relevant knowledge entries
   */
  async queryKnowledge(query, nResults = 3) {
    if (!this.isEnabled || !this.collection) {
      // Return mock knowledge for development
      return [
        {
          content: 'Daggerheart is a fantasy tabletop RPG with narrative-focused mechanics.',
          metadata: { category: 'general', type: 'overview' },
          distance: 0.1
        },
        {
          content: 'Characters have ancestries like Human, Elf, Dwarf, and classes like Guardian, Wizard, Ranger.',
          metadata: { category: 'character', type: 'creation' },
          distance: 0.2
        }
      ];
    }

    try {
      const results = await this.collection.query({
        queryTexts: [query],
        nResults: nResults
      });

      return results.documents[0].map((doc, index) => ({
        content: doc,
        metadata: results.metadatas[0][index],
        distance: results.distances[0][index]
      }));

    } catch (error) {
      console.error('Failed to query knowledge base:', error);
      return [];
    }
  }

  /**
   * Add new knowledge to the collection
   * @param {string} id - Unique identifier
   * @param {string} content - The knowledge content
   * @param {Object} metadata - Additional metadata
   */
  async addKnowledge(id, content, metadata = {}) {
    if (!this.collection) {
      throw new Error('ChromaDB not initialized');
    }

    try {
      await this.collection.add({
        ids: [id],
        documents: [content],
        metadatas: [metadata]
      });

      console.log('Added knowledge:', id);
    } catch (error) {
      console.error('Failed to add knowledge:', error);
      throw error;
    }
  }

  /**
   * Get contextual information for AI processing
   * @param {string} narrative - The narrative text to find context for
   * @param {string} entityType - Type of entity being generated
   * @returns {string} Formatted context string
   */
  async getContextForNarrative(narrative, entityType) {
    const queries = [
      narrative,
      `${entityType} creation`,
      `${entityType} rules`
    ];

    let allContext = [];

    for (const query of queries) {
      const results = await this.queryKnowledge(query, 2);
      allContext = allContext.concat(results);
    }

    // Remove duplicates and format
    const uniqueContext = allContext
      .filter((item, index, self) => 
        index === self.findIndex(t => t.content === item.content)
      )
      .slice(0, 5); // Limit to top 5 most relevant

    return uniqueContext
      .map(item => item.content)
      .join('\n\n');
  }

  /**
   * Clean up resources
   */
  async cleanup() {
    // ChromaDB client cleanup if needed
    this.client = null;
    this.collection = null;
  }
}

module.exports = ChromaDBService;
