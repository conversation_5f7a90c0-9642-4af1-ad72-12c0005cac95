const { ChromaClient } = require('chromadb');

class ChromaDBService {
  constructor() {
    this.client = null;
    this.collection = null;
    this.isEnabled = process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'placeholder_openai_key';

    if (!this.isEnabled) {
      console.warn('ChromaDB service disabled - OpenAI API key not configured');
    }
  }

  /**
   * Initialize ChromaDB client and collection
   */
  async initialize() {
    if (!this.isEnabled) {
      console.log('ChromaDB disabled - using mock data for development');
      return;
    }

    try {
      this.client = new ChromaClient({
        path: process.env.CHROMADB_PATH || 'http://localhost:8000'
      });

      // Create or get the Daggerheart knowledge collection
      this.collection = await this.client.getOrCreateCollection({
        name: 'daggerheart_knowledge'
      });

      console.log('ChromaDB initialized successfully');

      // Check if we need to populate the collection with initial data
      const count = await this.collection.count();
      if (count === 0) {
        await this.populateInitialData();
      }

    } catch (error) {
      console.error('Failed to initialize ChromaDB:', error);
      // Don't throw error in development mode
      if (process.env.NODE_ENV === 'development') {
        console.log('Continuing in development mode without ChromaDB');
        this.isEnabled = false;
      } else {
        throw error;
      }
    }
  }

  /**
   * Populate the collection with initial Daggerheart knowledge
   */
  async populateInitialData() {
    const daggerheartKnowledge = [
      // Ancestries/Races
      {
        id: 'ancestry_human',
        content: 'Humans in Daggerheart are versatile and adaptable. They have no specific physical traits but are known for their determination and ability to excel in any class. Humans often serve as leaders, diplomats, and pioneers.',
        metadata: { category: 'ancestry', type: 'human', subcategory: 'traits' }
      },
      {
        id: 'ancestry_elf',
        content: 'Elves are graceful beings with pointed ears and ethereal beauty. They have natural affinity for magic and archery. Elves live much longer than humans and often have a deep connection to nature and ancient knowledge.',
        metadata: { category: 'ancestry', type: 'elf', subcategory: 'traits' }
      },
      {
        id: 'ancestry_dwarf',
        content: 'Dwarves are stout, bearded folk known for their craftsmanship and resilience. They excel at working with stone and metal, creating masterwork weapons and armor. Dwarves are typically shorter but broader than humans.',
        metadata: { category: 'ancestry', type: 'dwarf', subcategory: 'traits' }
      },
      {
        id: 'ancestry_halfling',
        content: 'Halflings are small, cheerful people known for their luck and nimbleness. They are excellent at stealth and have a natural resistance to fear. Halflings value comfort, good food, and peaceful communities.',
        metadata: { category: 'ancestry', type: 'halfling', subcategory: 'traits' }
      },
      {
        id: 'ancestry_orc',
        content: 'Orcs are large, powerful beings with tusks and green or gray skin. They have incredible physical strength and endurance. Despite stereotypes, orcs can be honorable warriors, skilled crafters, or wise leaders.',
        metadata: { category: 'ancestry', type: 'orc', subcategory: 'traits' }
      },
      {
        id: 'ancestry_goblin',
        content: 'Goblins are small, clever creatures with large ears and sharp teeth. They are naturally curious and inventive, often creating ingenious devices. Goblins are quick and agile, excelling at stealth and trickery.',
        metadata: { category: 'ancestry', type: 'goblin', subcategory: 'traits' }
      },
      {
        id: 'ancestry_giant',
        content: 'Giants are massive humanoids with incredible strength. They come in various types (hill, stone, cloud, etc.) and often have elemental affinities. Giants can be gentle protectors or fearsome warriors.',
        metadata: { category: 'ancestry', type: 'giant', subcategory: 'traits' }
      },
      {
        id: 'ancestry_faerie',
        content: 'Faeries are small, magical beings with wings and an innate connection to magic. They can fly and have natural spellcasting abilities. Faeries are often mischievous but can be powerful allies.',
        metadata: { category: 'ancestry', type: 'faerie', subcategory: 'traits' }
      },

      // Classes
      {
        id: 'class_guardian',
        content: 'Guardians are stalwart protectors who excel at defense and protecting allies. They wear heavy armor, carry shields, and have abilities that draw enemy attacks. Guardians are the tanks of Daggerheart.',
        metadata: { category: 'class', type: 'guardian', subcategory: 'role' }
      },
      {
        id: 'class_seraph',
        content: 'Seraphs are divine healers and support characters. They channel divine magic to heal wounds, cure diseases, and provide buffs to allies. Seraphs often serve as the party\'s primary healer.',
        metadata: { category: 'class', type: 'seraph', subcategory: 'role' }
      },
      {
        id: 'class_wizard',
        content: 'Wizards are scholarly spellcasters who study arcane magic. They have access to a wide variety of spells and can adapt to many situations. Wizards are typically fragile but incredibly versatile.',
        metadata: { category: 'class', type: 'wizard', subcategory: 'role' }
      },
      {
        id: 'class_ranger',
        content: 'Rangers are nature warriors who excel at tracking, survival, and ranged combat. They have a deep connection to the natural world and often serve as scouts and guides for their parties.',
        metadata: { category: 'class', type: 'ranger', subcategory: 'role' }
      },
      {
        id: 'class_rogue',
        content: 'Rogues are stealthy specialists who excel at sneaking, picking locks, and dealing precise damage. They are the skill experts of the party and often handle delicate situations requiring finesse.',
        metadata: { category: 'class', type: 'rogue', subcategory: 'role' }
      },
      {
        id: 'class_bard',
        content: 'Bards are charismatic performers who use music and stories to cast spells and inspire allies. They are excellent at social encounters and provide versatile support to their party.',
        metadata: { category: 'class', type: 'bard', subcategory: 'role' }
      },
      {
        id: 'class_sorcerer',
        content: 'Sorcerers have innate magical power that flows through their blood. Their magic is more instinctual and chaotic than a wizard\'s studied approach. Sorcerers can be incredibly powerful but unpredictable.',
        metadata: { category: 'class', type: 'sorcerer', subcategory: 'role' }
      },
      {
        id: 'class_warrior',
        content: 'Warriors are martial fighters who excel at weapon combat. They are versatile combatants who can adapt to many fighting styles and weapons. Warriors are the backbone of any fighting force.',
        metadata: { category: 'class', type: 'warrior', subcategory: 'role' }
      },

      // Equipment and Items
      {
        id: 'equipment_weapons',
        content: 'Common weapons in Daggerheart include swords, axes, bows, daggers, maces, and staves. Magical weapons may have enchantments like flaming blades, frost arrows, or weapons that glow with inner light.',
        metadata: { category: 'equipment', type: 'weapons', subcategory: 'types' }
      },
      {
        id: 'equipment_armor',
        content: 'Armor ranges from leather and cloth to chainmail and plate armor. Magical armor might provide elemental resistance, enhanced protection, or special abilities like silent movement.',
        metadata: { category: 'equipment', type: 'armor', subcategory: 'types' }
      },
      {
        id: 'equipment_magical_items',
        content: 'Magical items include rings of power, cloaks of protection, boots of speed, bags of holding, and various potions. These items often have rich histories and unique properties.',
        metadata: { category: 'equipment', type: 'magical', subcategory: 'examples' }
      },

      // Locations and Environments
      {
        id: 'location_taverns',
        content: 'Taverns are common gathering places with warm hearths, flowing ale, and rooms for rent. They often serve as adventure starting points and information hubs. Famous taverns might have unique atmospheres or magical properties.',
        metadata: { category: 'location', type: 'tavern', subcategory: 'description' }
      },
      {
        id: 'location_dungeons',
        content: 'Dungeons are dangerous underground complexes filled with monsters, traps, and treasure. They might be ancient ruins, abandoned mines, or lairs of powerful creatures. Each dungeon has its own theme and challenges.',
        metadata: { category: 'location', type: 'dungeon', subcategory: 'description' }
      },
      {
        id: 'location_forests',
        content: 'Forests can be peaceful groves or dark, dangerous woodlands. They might be home to druids, fey creatures, or dangerous beasts. Ancient forests often have magical properties and hidden secrets.',
        metadata: { category: 'location', type: 'forest', subcategory: 'description' }
      },
      {
        id: 'location_cities',
        content: 'Cities are bustling centers of commerce and culture. They have markets, guilds, temples, and various districts. Cities offer opportunities for trade, information gathering, and political intrigue.',
        metadata: { category: 'location', type: 'city', subcategory: 'description' }
      }
    ];

    try {
      await this.collection.add({
        ids: daggerheartKnowledge.map(item => item.id),
        documents: daggerheartKnowledge.map(item => item.content),
        metadatas: daggerheartKnowledge.map(item => item.metadata)
      });

      console.log('Initial Daggerheart knowledge populated');
    } catch (error) {
      console.error('Failed to populate initial data:', error);
    }
  }

  /**
   * Query the knowledge base for relevant context
   * @param {string} query - The query text
   * @param {number} nResults - Number of results to return
   * @returns {Array} Relevant knowledge entries
   */
  async queryKnowledge(query, nResults = 3) {
    if (!this.isEnabled || !this.collection) {
      // Return mock knowledge for development
      return [
        {
          content: 'Daggerheart is a fantasy tabletop RPG with narrative-focused mechanics.',
          metadata: { category: 'general', type: 'overview' },
          distance: 0.1
        },
        {
          content: 'Characters have ancestries like Human, Elf, Dwarf, and classes like Guardian, Wizard, Ranger.',
          metadata: { category: 'character', type: 'creation' },
          distance: 0.2
        }
      ];
    }

    try {
      const results = await this.collection.query({
        queryTexts: [query],
        nResults: nResults
      });

      return results.documents[0].map((doc, index) => ({
        content: doc,
        metadata: results.metadatas[0][index],
        distance: results.distances[0][index]
      }));

    } catch (error) {
      console.error('Failed to query knowledge base:', error);
      return [];
    }
  }

  /**
   * Add new knowledge to the collection
   * @param {string} id - Unique identifier
   * @param {string} content - The knowledge content
   * @param {Object} metadata - Additional metadata
   */
  async addKnowledge(id, content, metadata = {}) {
    if (!this.collection) {
      throw new Error('ChromaDB not initialized');
    }

    try {
      await this.collection.add({
        ids: [id],
        documents: [content],
        metadatas: [metadata]
      });

      console.log('Added knowledge:', id);
    } catch (error) {
      console.error('Failed to add knowledge:', error);
      throw error;
    }
  }

  /**
   * Get contextual information for AI processing
   * @param {string} narrative - The narrative text to find context for
   * @param {string} entityType - Type of entity being generated
   * @returns {string} Formatted context string
   */
  async getContextForNarrative(narrative, entityType) {
    if (!this.isEnabled) {
      // Return enhanced mock context based on entity type
      return this.getMockContextForEntityType(entityType, narrative);
    }

    // Create targeted queries based on entity type and narrative content
    const queries = this.buildSmartQueries(narrative, entityType);
    let allContext = [];

    for (const query of queries) {
      const results = await this.queryKnowledge(query, 3);
      allContext = allContext.concat(results);
    }

    // Remove duplicates and format
    const uniqueContext = allContext
      .filter((item, index, self) =>
        index === self.findIndex(t => t.content === item.content)
      )
      .slice(0, 8); // Increased to top 8 most relevant

    return uniqueContext
      .map(item => item.content)
      .join('\n\n');
  }

  buildSmartQueries(narrative, entityType) {
    const baseQueries = [narrative, `${entityType} creation`];

    // Add specific queries based on detected keywords
    const keywords = narrative.toLowerCase();

    if (entityType === 'character') {
      if (keywords.includes('elf') || keywords.includes('pointed ears')) {
        baseQueries.push('elf ancestry traits');
      }
      if (keywords.includes('dwarf') || keywords.includes('beard') || keywords.includes('stout')) {
        baseQueries.push('dwarf ancestry traits');
      }
      if (keywords.includes('magic') || keywords.includes('spell') || keywords.includes('wizard')) {
        baseQueries.push('wizard class spellcaster');
      }
      if (keywords.includes('sword') || keywords.includes('warrior') || keywords.includes('fighter')) {
        baseQueries.push('warrior class combat');
      }
      if (keywords.includes('heal') || keywords.includes('divine') || keywords.includes('holy')) {
        baseQueries.push('seraph class healer');
      }
    } else if (entityType === 'location') {
      if (keywords.includes('tavern') || keywords.includes('inn') || keywords.includes('ale')) {
        baseQueries.push('tavern location description');
      }
      if (keywords.includes('dungeon') || keywords.includes('underground') || keywords.includes('cave')) {
        baseQueries.push('dungeon location description');
      }
      if (keywords.includes('forest') || keywords.includes('tree') || keywords.includes('wood')) {
        baseQueries.push('forest location description');
      }
      if (keywords.includes('city') || keywords.includes('town') || keywords.includes('market')) {
        baseQueries.push('city location description');
      }
    } else if (entityType === 'object') {
      if (keywords.includes('sword') || keywords.includes('blade') || keywords.includes('weapon')) {
        baseQueries.push('weapons equipment types');
      }
      if (keywords.includes('armor') || keywords.includes('shield') || keywords.includes('protection')) {
        baseQueries.push('armor equipment types');
      }
      if (keywords.includes('magic') || keywords.includes('enchant') || keywords.includes('glow')) {
        baseQueries.push('magical items equipment');
      }
    }

    return baseQueries;
  }

  getMockContextForEntityType(entityType, narrative) {
    const mockContexts = {
      character: `
DAGGERHEART CHARACTER CREATION:
- Ancestries include Human (versatile), Elf (magical, pointed ears), Dwarf (stout, bearded, crafters), Halfling (small, lucky), Orc (strong, tusked), Goblin (small, clever), Giant (massive), Faerie (tiny, winged, magical)
- Classes include Guardian (tank/protector), Seraph (divine healer), Wizard (arcane scholar), Ranger (nature warrior), Rogue (stealth specialist), Bard (charismatic performer), Sorcerer (innate magic), Warrior (martial fighter)
- Characters should have distinct personalities, backgrounds, and motivations
- Equipment should match their class and role
- Physical descriptions should reflect their ancestry and lifestyle`,

      location: `
DAGGERHEART LOCATIONS:
- Taverns: Warm gathering places with hearths, ale, rooms for rent, and local gossip
- Dungeons: Dangerous underground complexes with monsters, traps, and ancient treasures
- Forests: Can be peaceful groves or dark woodlands, home to druids, fey, or beasts
- Cities: Bustling centers with markets, guilds, temples, and political intrigue
- Locations should have atmosphere, inhabitants, secrets, and story hooks
- Consider sensory details: sounds, smells, lighting, temperature
- Include both obvious features and hidden elements`,

      object: `
DAGGERHEART EQUIPMENT:
- Weapons: Swords, axes, bows, daggers, maces, staves - can be mundane or magical
- Armor: Leather, chainmail, plate armor - provides protection and can have special properties
- Magical Items: Rings, cloaks, boots, potions, wands - have unique abilities and histories
- Items should have appropriate rarity: common, uncommon, rare, very rare, legendary, artifact
- Consider craftsmanship, materials, condition, and previous owners
- Magical items should have interesting properties and limitations`
    };

    return mockContexts[entityType] || 'General Daggerheart fantasy RPG context for worldbuilding and character creation.';
  }

  /**
   * Clean up resources
   */
  async cleanup() {
    // ChromaDB client cleanup if needed
    this.client = null;
    this.collection = null;
  }
}

module.exports = ChromaDBService;
