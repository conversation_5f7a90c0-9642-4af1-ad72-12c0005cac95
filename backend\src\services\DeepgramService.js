const { createClient } = require('@deepgram/sdk');

class DeepgramService {
  constructor() {
    this.isEnabled = process.env.DEEPGRAM_API_KEY && process.env.DEEPGRAM_API_KEY !== 'placeholder_deepgram_key';
    this.deepgram = this.isEnabled ? createClient(process.env.DEEPGRAM_API_KEY) : null;
    this.connections = new Map(); // Store active connections by socket ID

    if (!this.isEnabled) {
      console.warn('Deepgram service disabled - API key not configured');
    }
  }

  /**
   * Start live transcription for a client
   * @param {string} socketId - Client socket ID
   * @param {Function} onTranscript - Callback for transcript results
   * @param {Function} onError - Callback for errors
   */
  async startLiveTranscription(socketId, onTranscript, onError) {
    if (!this.isEnabled) {
      console.log('Deepgram disabled - simulating transcription for development');
      // Simulate more realistic development transcription
      const mockTranscripts = [
        'A mysterious figure enters the tavern',
        'They have a long flowing cloak and piercing blue eyes',
        'The stranger approaches your table with a confident stride',
        'You notice they carry an ornate staff with glowing runes'
      ];

      let index = 0;
      const interval = setInterval(() => {
        if (index < mockTranscripts.length) {
          onTranscript({
            transcript: mockTranscripts[index],
            is_final: true,
            confidence: 0.95
          });
          index++;
        } else {
          clearInterval(interval);
        }
      }, 3000);

      return { mockInterval: interval };
    }

    try {
      const connection = this.deepgram.listen.live({
        model: 'nova-2',
        language: 'en-US',
        smart_format: true,
        interim_results: true,
        utterance_end_ms: 1500,
        vad_events: true,
        encoding: 'linear16',
        sample_rate: 16000,
        channels: 1,
        punctuate: true,
        profanity_filter: false,
        redact: false,
        diarize: false,
        multichannel: false,
        alternatives: 1,
        numerals: true,
        search: [],
        replace: [],
        keywords: ['daggerheart', 'character', 'location', 'tavern', 'dungeon', 'magic', 'spell', 'weapon', 'armor']
      });

      // Store connection for this socket
      this.connections.set(socketId, connection);

      // Handle transcript results
      connection.on('Results', (data) => {
        const transcript = data.channel?.alternatives?.[0]?.transcript;
        if (transcript && transcript.trim()) {
          onTranscript({
            transcript,
            is_final: data.is_final,
            confidence: data.channel?.alternatives?.[0]?.confidence
          });
        }
      });

      // Handle errors
      connection.on('error', (error) => {
        console.error('Deepgram error for socket', socketId, ':', error);
        this.connections.delete(socketId);
        onError(error);
      });

      // Handle connection close
      connection.on('close', () => {
        console.log('Deepgram connection closed for socket:', socketId);
        this.connections.delete(socketId);
      });

      // Handle connection open
      connection.on('open', () => {
        console.log('Deepgram connection opened for socket:', socketId);
      });

      // Handle metadata
      connection.on('Metadata', (data) => {
        console.log('Deepgram metadata for socket', socketId, ':', data);
      });

      console.log('Deepgram live transcription started for socket:', socketId);
      return connection;

    } catch (error) {
      console.error('Failed to start Deepgram transcription:', error);
      onError(error);
      throw error;
    }
  }

  /**
   * Send audio data to Deepgram
   * @param {string} socketId - Client socket ID
   * @param {Buffer} audioData - Audio data buffer
   */
  sendAudio(socketId, audioData) {
    const connection = this.connections.get(socketId);
    if (connection && connection.getReadyState() === 1) {
      try {
        connection.send(audioData);
      } catch (error) {
        console.error('Error sending audio to Deepgram for socket', socketId, ':', error);
        this.connections.delete(socketId);
      }
    } else {
      console.warn('No active Deepgram connection for socket:', socketId, 'Ready state:', connection?.getReadyState());
    }
  }

  /**
   * Stop transcription for a client
   * @param {string} socketId - Client socket ID
   */
  stopTranscription(socketId) {
    const connection = this.connections.get(socketId);
    if (connection) {
      connection.finish();
      this.connections.delete(socketId);
      console.log('Stopped Deepgram transcription for socket:', socketId);
    }
  }

  /**
   * Clean up all connections (for server shutdown)
   */
  cleanup() {
    for (const [socketId, connection] of this.connections) {
      connection.finish();
    }
    this.connections.clear();
  }
}

module.exports = DeepgramService;
