const OpenAI = require('openai');

class OpenAIService {
  constructor() {
    this.isEnabled = process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'placeholder_openai_key';
    this.openai = this.isEnabled ? new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    }) : null;

    if (!this.isEnabled) {
      console.warn('OpenAI service disabled - API key not configured');
    }
  }

  /**
   * Generate character data from narrative text
   * @param {string} narrative - The transcribed narrative text
   * @param {string} context - Additional context from RAG system
   * @returns {Object} Character data structure
   */
  async generateCharacterData(narrative, context = '') {
    if (!this.isEnabled) {
      // Return mock data for development
      return {
        name: "Mock Character",
        race: "Human",
        class: "Warrior",
        appearance: {
          size: "medium",
          description: "A sturdy figure with determined eyes",
          notable_features: ["Scar across left cheek", "Weathered hands"]
        },
        personality: {
          traits: ["Brave", "Loyal"],
          demeanor: "Stoic but kind"
        },
        abilities: {
          suggested_stats: "High Strength and Constitution",
          special_abilities: ["Combat Training", "Leadership"]
        },
        background: {
          occupation: "Town Guard",
          origin: "Small farming village"
        },
        equipment: ["Longsword", "Chain mail", "Shield"],
        notes: "Development mode - OpenAI not configured"
      };
    }

    const systemPrompt = `You are an expert Daggerheart RPG assistant. Based on the DM's narrative description, extract and infer character information to fill out a character sheet.

Context about Daggerheart rules:
${context}

Return a JSON object with the following structure:
{
  "name": "character name if mentioned, or null",
  "race": "inferred race or null",
  "class": "inferred class or null",
  "appearance": {
    "size": "tiny/small/medium/large/huge or null",
    "description": "physical description",
    "notable_features": ["array of notable features"]
  },
  "personality": {
    "traits": ["personality traits"],
    "demeanor": "overall demeanor"
  },
  "abilities": {
    "suggested_stats": "brief stat suggestions or null",
    "special_abilities": ["inferred abilities"]
  },
  "background": {
    "occupation": "inferred occupation or null",
    "origin": "inferred background or null"
  },
  "equipment": ["inferred equipment or weapons"],
  "notes": "additional relevant information"
}

Only include information that can be reasonably inferred from the narrative. Use null for unknown fields.`;

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: `DM Narrative: "${narrative}"` }
        ],
        temperature: 0.7,
        max_tokens: 1000,
        response_format: { type: 'json_object' }
      });

      return JSON.parse(response.choices[0].message.content);
    } catch (error) {
      console.error('Error generating character data:', error);
      throw error;
    }
  }

  /**
   * Generate location data from narrative text
   * @param {string} narrative - The transcribed narrative text
   * @param {string} context - Additional context from RAG system
   * @returns {Object} Location data structure
   */
  async generateLocationData(narrative, context = '') {
    const systemPrompt = `You are an expert Daggerheart RPG assistant. Based on the DM's narrative description, extract and infer location information.

Context about Daggerheart rules:
${context}

Return a JSON object with the following structure:
{
  "name": "location name if mentioned, or null",
  "type": "tavern/dungeon/forest/city/etc or null",
  "description": "detailed description of the location",
  "atmosphere": "mood and feeling of the place",
  "features": {
    "notable_landmarks": ["array of landmarks"],
    "hazards": ["potential dangers or hazards"],
    "resources": ["available resources or services"]
  },
  "inhabitants": ["types of creatures or people found here"],
  "connections": ["connected locations or areas"],
  "secrets": ["hidden aspects or secrets"],
  "notes": "additional relevant information"
}

Only include information that can be reasonably inferred from the narrative. Use null for unknown fields.`;

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: `DM Narrative: "${narrative}"` }
        ],
        temperature: 0.7,
        max_tokens: 1000,
        response_format: { type: 'json_object' }
      });

      return JSON.parse(response.choices[0].message.content);
    } catch (error) {
      console.error('Error generating location data:', error);
      throw error;
    }
  }

  /**
   * Generate object/item data from narrative text
   * @param {string} narrative - The transcribed narrative text
   * @param {string} context - Additional context from RAG system
   * @returns {Object} Object data structure
   */
  async generateObjectData(narrative, context = '') {
    const systemPrompt = `You are an expert Daggerheart RPG assistant. Based on the DM's narrative description, extract and infer object/item information.

Context about Daggerheart rules:
${context}

Return a JSON object with the following structure:
{
  "name": "object name if mentioned, or null",
  "type": "weapon/armor/tool/treasure/consumable/etc or null",
  "description": "detailed description of the object",
  "properties": {
    "material": "what it's made of",
    "size": "physical size",
    "weight": "approximate weight",
    "condition": "current condition"
  },
  "mechanical_effects": {
    "damage": "weapon damage if applicable",
    "armor_value": "armor rating if applicable", 
    "special_properties": ["magical or special effects"]
  },
  "value": {
    "estimated_cost": "approximate monetary value",
    "rarity": "common/uncommon/rare/legendary"
  },
  "history": "background or origin story",
  "notes": "additional relevant information"
}

Only include information that can be reasonably inferred from the narrative. Use null for unknown fields.`;

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: `DM Narrative: "${narrative}"` }
        ],
        temperature: 0.7,
        max_tokens: 1000,
        response_format: { type: 'json_object' }
      });

      return JSON.parse(response.choices[0].message.content);
    } catch (error) {
      console.error('Error generating object data:', error);
      throw error;
    }
  }

  /**
   * Generate image using DALL-E 3
   * @param {Object} entityData - The entity data to create image for
   * @param {string} entityType - Type of entity (character/location/object)
   * @returns {string} Image URL
   */
  async generateImage(entityData, entityType) {
    let prompt = '';

    switch (entityType) {
      case 'character':
        prompt = this._buildCharacterImagePrompt(entityData);
        break;
      case 'location':
        prompt = this._buildLocationImagePrompt(entityData);
        break;
      case 'object':
        prompt = this._buildObjectImagePrompt(entityData);
        break;
      default:
        throw new Error('Invalid entity type for image generation');
    }

    try {
      const response = await this.openai.images.generate({
        model: 'dall-e-3',
        prompt: prompt,
        size: '1024x1024',
        quality: 'standard',
        n: 1,
      });

      return response.data[0].url;
    } catch (error) {
      console.error('Error generating image:', error);
      throw error;
    }
  }

  _buildCharacterImagePrompt(characterData) {
    let prompt = 'Fantasy RPG character portrait, ';
    
    if (characterData.race) prompt += `${characterData.race} `;
    if (characterData.class) prompt += `${characterData.class}, `;
    if (characterData.appearance?.description) prompt += `${characterData.appearance.description}, `;
    if (characterData.appearance?.notable_features) {
      prompt += characterData.appearance.notable_features.join(', ') + ', ';
    }
    
    prompt += 'detailed fantasy art style, high quality, professional illustration';
    
    return prompt;
  }

  _buildLocationImagePrompt(locationData) {
    let prompt = 'Fantasy RPG location, ';
    
    if (locationData.type) prompt += `${locationData.type}, `;
    if (locationData.description) prompt += `${locationData.description}, `;
    if (locationData.atmosphere) prompt += `${locationData.atmosphere} atmosphere, `;
    
    prompt += 'detailed fantasy art style, environmental concept art, high quality';
    
    return prompt;
  }

  _buildObjectImagePrompt(objectData) {
    let prompt = 'Fantasy RPG item, ';
    
    if (objectData.type) prompt += `${objectData.type}, `;
    if (objectData.description) prompt += `${objectData.description}, `;
    if (objectData.properties?.material) prompt += `made of ${objectData.properties.material}, `;
    
    prompt += 'detailed fantasy art style, item illustration, high quality, clean background';
    
    return prompt;
  }
}

module.exports = OpenAIService;
