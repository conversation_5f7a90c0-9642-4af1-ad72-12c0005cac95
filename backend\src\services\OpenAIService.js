const OpenAI = require('openai');

class OpenAIService {
  constructor() {
    this.isEnabled = process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'placeholder_openai_key';
    this.openai = this.isEnabled ? new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    }) : null;

    if (!this.isEnabled) {
      console.warn('OpenAI service disabled - API key not configured');
    }
  }

  /**
   * Generate character data from narrative text
   * @param {string} narrative - The transcribed narrative text
   * @param {string} context - Additional context from RAG system
   * @returns {Object} Character data structure
   */
  async generateCharacterData(narrative, context = '') {
    if (!this.isEnabled) {
      // Return mock data for development
      return {
        name: "Mock Character",
        race: "Human",
        class: "Warrior",
        appearance: {
          size: "medium",
          description: "A sturdy figure with determined eyes",
          notable_features: ["Scar across left cheek", "Weathered hands"]
        },
        personality: {
          traits: ["Brave", "Loyal"],
          demeanor: "Stoic but kind"
        },
        abilities: {
          suggested_stats: "High Strength and Constitution",
          special_abilities: ["Combat Training", "Leadership"]
        },
        background: {
          occupation: "Town Guard",
          origin: "Small farming village"
        },
        equipment: ["Longsword", "Chain mail", "Shield"],
        notes: "Development mode - OpenAI not configured"
      };
    }

    const systemPrompt = `You are an expert Daggerheart RPG assistant and master storyteller. Your role is to analyze a DM's narrative description and intelligently extract character information while filling in reasonable details that enhance the story.

DAGGERHEART CONTEXT:
${context}

CORE PRINCIPLES:
- Extract explicit information from the narrative first
- Infer logical details that fit the described character
- Create cohesive, interesting characters that feel alive
- Use Daggerheart-appropriate terminology and concepts
- Maintain consistency with fantasy RPG conventions

RESPONSE FORMAT - Return a JSON object with this exact structure:
{
  "name": "character name if mentioned, or generate an appropriate fantasy name",
  "race": "inferred race (Human, Elf, Dwarf, Halfling, Orc, Goblin, Giant, Faerie, etc.) or null",
  "class": "inferred class (Guardian, Seraph, Wizard, Ranger, Rogue, Bard, Sorcerer, Warrior, etc.) or null",
  "appearance": {
    "size": "tiny/small/medium/large/huge based on race and description",
    "description": "detailed physical description expanding on narrative",
    "notable_features": ["specific memorable physical traits"]
  },
  "personality": {
    "traits": ["2-4 personality traits inferred from behavior/description"],
    "demeanor": "overall personality and attitude"
  },
  "abilities": {
    "suggested_stats": "brief suggestions for primary stats based on class/role",
    "special_abilities": ["abilities that match class and narrative context"]
  },
  "background": {
    "occupation": "current or former profession",
    "origin": "where they might be from or their background"
  },
  "equipment": ["weapons, armor, and notable items they likely carry"],
  "notes": "additional story hooks, motivations, or interesting details"
}

GUIDELINES:
- If no name is given, create an appropriate fantasy name for the race/culture
- Infer race from physical descriptions (pointed ears = elf, beard/stout = dwarf, etc.)
- Match class to described abilities, weapons, or role
- Expand descriptions while staying true to the narrative
- Add equipment that makes sense for their class and situation
- Include personality traits that create roleplay opportunities`;

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: `DM Narrative: "${narrative}"` }
        ],
        temperature: 0.7,
        max_tokens: 1000,
        response_format: { type: 'json_object' }
      });

      return JSON.parse(response.choices[0].message.content);
    } catch (error) {
      console.error('Error generating character data:', error);
      throw error;
    }
  }

  /**
   * Generate location data from narrative text
   * @param {string} narrative - The transcribed narrative text
   * @param {string} context - Additional context from RAG system
   * @returns {Object} Location data structure
   */
  async generateLocationData(narrative, context = '') {
    const systemPrompt = `You are an expert Daggerheart RPG worldbuilder and environmental storyteller. Analyze the DM's narrative to create rich, immersive locations that enhance the gaming experience.

DAGGERHEART CONTEXT:
${context}

CORE PRINCIPLES:
- Extract explicit details from the narrative first
- Expand with logical, atmospheric details that fit the setting
- Create locations that feel lived-in and purposeful
- Include both obvious and subtle story elements
- Consider the location's role in the broader world

RESPONSE FORMAT - Return a JSON object with this exact structure:
{
  "name": "location name if mentioned, or create an evocative name",
  "type": "specific location type (tavern, dungeon, forest, city, temple, castle, village, cave, etc.)",
  "description": "rich, detailed description that brings the location to life",
  "atmosphere": "the mood, feeling, and sensory experience of being there",
  "features": {
    "notable_landmarks": ["distinctive features that make this place memorable"],
    "hazards": ["potential dangers, traps, or environmental threats"],
    "resources": ["services, items, or advantages available here"]
  },
  "inhabitants": ["types of creatures, NPCs, or groups that live or frequent here"],
  "connections": ["how this location connects to other places or areas"],
  "secrets": ["hidden elements, mysteries, or story hooks"],
  "notes": "additional worldbuilding details, history, or adventure hooks"
}

GUIDELINES:
- Create evocative names that fit the fantasy setting
- Include sensory details (sounds, smells, lighting, temperature)
- Consider the location's purpose and how it serves the story
- Add layers of detail from obvious to hidden
- Include both immediate and long-term story potential
- Make inhabitants feel authentic to the location type
- Suggest connections that expand the world naturally`;

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: `DM Narrative: "${narrative}"` }
        ],
        temperature: 0.7,
        max_tokens: 1000,
        response_format: { type: 'json_object' }
      });

      return JSON.parse(response.choices[0].message.content);
    } catch (error) {
      console.error('Error generating location data:', error);
      throw error;
    }
  }

  /**
   * Generate object/item data from narrative text
   * @param {string} narrative - The transcribed narrative text
   * @param {string} context - Additional context from RAG system
   * @returns {Object} Object data structure
   */
  async generateObjectData(narrative, context = '') {
    const systemPrompt = `You are an expert Daggerheart RPG item designer and magical artificer. Analyze the DM's narrative to create compelling items that enhance gameplay and storytelling.

DAGGERHEART CONTEXT:
${context}

CORE PRINCIPLES:
- Extract explicit details from the narrative first
- Design items that feel authentic to the fantasy world
- Balance mechanical utility with narrative significance
- Consider the item's role in the story and world
- Create items that inspire player interaction and creativity

RESPONSE FORMAT - Return a JSON object with this exact structure:
{
  "name": "item name if mentioned, or create an evocative, memorable name",
  "type": "specific item category (weapon, armor, tool, treasure, consumable, magical, artifact, etc.)",
  "description": "rich, detailed description including appearance, craftsmanship, and notable features",
  "properties": {
    "material": "what it's crafted from (steel, oak, mithril, dragonscale, etc.)",
    "size": "physical dimensions and portability",
    "weight": "approximate weight and how it feels to handle",
    "condition": "current state (pristine, worn, damaged, ancient, etc.)"
  },
  "mechanical_effects": {
    "damage": "weapon damage and type if applicable",
    "armor_value": "protection rating if applicable",
    "special_properties": ["magical effects, enchantments, or unique abilities"]
  },
  "value": {
    "estimated_cost": "monetary value in appropriate currency",
    "rarity": "common/uncommon/rare/very rare/legendary/artifact"
  },
  "history": "origin story, previous owners, or significant events",
  "notes": "additional lore, usage tips, or story hooks"
}

GUIDELINES:
- Create names that reflect the item's nature and significance
- Include tactile and visual details that make the item feel real
- Match mechanical effects to the item's apparent power level
- Consider who made it, why, and how it came to be here
- Add history that creates story opportunities
- Balance power with interesting limitations or quirks
- Make even mundane items feel special through description`;

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: `DM Narrative: "${narrative}"` }
        ],
        temperature: 0.7,
        max_tokens: 1000,
        response_format: { type: 'json_object' }
      });

      return JSON.parse(response.choices[0].message.content);
    } catch (error) {
      console.error('Error generating object data:', error);
      throw error;
    }
  }

  /**
   * Generate image using DALL-E 3
   * @param {Object} entityData - The entity data to create image for
   * @param {string} entityType - Type of entity (character/location/object)
   * @returns {string} Image URL
   */
  async generateImage(entityData, entityType) {
    let prompt = '';

    switch (entityType) {
      case 'character':
        prompt = this._buildCharacterImagePrompt(entityData);
        break;
      case 'location':
        prompt = this._buildLocationImagePrompt(entityData);
        break;
      case 'object':
        prompt = this._buildObjectImagePrompt(entityData);
        break;
      default:
        throw new Error('Invalid entity type for image generation');
    }

    try {
      const response = await this.openai.images.generate({
        model: 'dall-e-3',
        prompt: prompt,
        size: '1024x1024',
        quality: 'standard',
        n: 1,
      });

      return response.data[0].url;
    } catch (error) {
      console.error('Error generating image:', error);
      throw error;
    }
  }

  _buildCharacterImagePrompt(characterData) {
    let prompt = 'Fantasy RPG character portrait in detailed digital art style. ';

    // Add race/ancestry information
    if (characterData.race) {
      prompt += `${characterData.race} `;
      // Add race-specific details
      if (characterData.race.toLowerCase().includes('elf')) {
        prompt += 'with pointed ears and ethereal features, ';
      } else if (characterData.race.toLowerCase().includes('dwarf')) {
        prompt += 'with a magnificent beard and stout build, ';
      } else if (characterData.race.toLowerCase().includes('orc')) {
        prompt += 'with tusks and powerful build, ';
      } else if (characterData.race.toLowerCase().includes('halfling')) {
        prompt += 'with small stature and cheerful expression, ';
      }
    }

    // Add class information
    if (characterData.class) {
      prompt += `${characterData.class} `;
      // Add class-specific equipment hints
      if (characterData.class.toLowerCase().includes('wizard')) {
        prompt += 'holding a magical staff or spellbook, wearing robes, ';
      } else if (characterData.class.toLowerCase().includes('warrior')) {
        prompt += 'in armor with weapons, ';
      } else if (characterData.class.toLowerCase().includes('rogue')) {
        prompt += 'in dark leather armor with daggers, ';
      } else if (characterData.class.toLowerCase().includes('guardian')) {
        prompt += 'in heavy armor with shield, ';
      }
    }

    // Add physical description
    if (characterData.appearance?.description) {
      prompt += `${characterData.appearance.description}, `;
    }

    // Add notable features
    if (characterData.appearance?.notable_features && characterData.appearance.notable_features.length > 0) {
      prompt += characterData.appearance.notable_features.join(', ') + ', ';
    }

    // Add personality hints for expression
    if (characterData.personality?.demeanor) {
      prompt += `with a ${characterData.personality.demeanor} expression, `;
    }

    // Add equipment
    if (characterData.equipment && characterData.equipment.length > 0) {
      const equipment = characterData.equipment.slice(0, 3).join(', '); // Limit to avoid prompt bloat
      prompt += `carrying ${equipment}, `;
    }

    prompt += 'professional fantasy art, detailed character design, dramatic lighting, high quality digital painting, D&D character art style';

    return prompt;
  }

  _buildLocationImagePrompt(locationData) {
    let prompt = 'Fantasy RPG environment concept art. ';

    // Add location type with specific details
    if (locationData.type) {
      prompt += `${locationData.type} `;

      // Add type-specific atmospheric details
      if (locationData.type.toLowerCase().includes('tavern')) {
        prompt += 'with warm lighting, wooden beams, and cozy interior, ';
      } else if (locationData.type.toLowerCase().includes('dungeon')) {
        prompt += 'with stone corridors, torchlight, and mysterious shadows, ';
      } else if (locationData.type.toLowerCase().includes('forest')) {
        prompt += 'with ancient trees, dappled sunlight, and mystical atmosphere, ';
      } else if (locationData.type.toLowerCase().includes('city')) {
        prompt += 'with bustling streets, medieval architecture, and busy marketplace, ';
      }
    }

    // Add main description
    if (locationData.description) {
      prompt += `${locationData.description}, `;
    }

    // Add atmospheric details
    if (locationData.atmosphere) {
      prompt += `${locationData.atmosphere} mood and atmosphere, `;
    }

    // Add notable landmarks
    if (locationData.features?.notable_landmarks && locationData.features.notable_landmarks.length > 0) {
      const landmarks = locationData.features.notable_landmarks.slice(0, 2).join(', ');
      prompt += `featuring ${landmarks}, `;
    }

    // Add inhabitants for scale and life
    if (locationData.inhabitants && locationData.inhabitants.length > 0) {
      const inhabitants = locationData.inhabitants.slice(0, 2).join(' and ');
      prompt += `inhabited by ${inhabitants}, `;
    }

    prompt += 'detailed fantasy environment art, cinematic composition, atmospheric lighting, high quality digital painting, D&D setting artwork';

    return prompt;
  }

  _buildObjectImagePrompt(objectData) {
    let prompt = 'Fantasy RPG item illustration. ';

    // Add item type with specific details
    if (objectData.type) {
      prompt += `${objectData.type} `;

      // Add type-specific details
      if (objectData.type.toLowerCase().includes('weapon')) {
        prompt += 'with intricate craftsmanship and sharp edges, ';
      } else if (objectData.type.toLowerCase().includes('armor')) {
        prompt += 'with protective plating and detailed metalwork, ';
      } else if (objectData.type.toLowerCase().includes('magical')) {
        prompt += 'glowing with mystical energy and arcane symbols, ';
      }
    }

    // Add detailed description
    if (objectData.description) {
      prompt += `${objectData.description}, `;
    }

    // Add material information
    if (objectData.properties?.material) {
      prompt += `crafted from ${objectData.properties.material}, `;
    }

    // Add condition details
    if (objectData.properties?.condition) {
      prompt += `in ${objectData.properties.condition} condition, `;
    }

    // Add magical properties
    if (objectData.mechanical_effects?.special_properties && objectData.mechanical_effects.special_properties.length > 0) {
      const properties = objectData.mechanical_effects.special_properties.slice(0, 2).join(' and ');
      prompt += `with ${properties}, `;
    }

    // Add rarity-based visual effects
    if (objectData.value?.rarity) {
      const rarity = objectData.value.rarity.toLowerCase();
      if (rarity.includes('legendary') || rarity.includes('artifact')) {
        prompt += 'radiating powerful magical aura, ';
      } else if (rarity.includes('rare') || rarity.includes('very rare')) {
        prompt += 'with subtle magical glow, ';
      }
    }

    prompt += 'detailed item art, professional game asset, clean background, dramatic lighting, high quality digital illustration, D&D equipment style';

    return prompt;
  }
}

module.exports = OpenAIService;
