const DeepgramService = require('./DeepgramService');
const OpenAIService = require('./OpenAIService');
const ChromaDBService = require('./ChromaDBService');

class SocketHandler {
  constructor(io) {
    this.io = io;
    this.deepgramService = new DeepgramService();
    this.openaiService = new OpenAIService();
    this.chromaService = new ChromaDBService();
    
    // Store client states
    this.clientStates = new Map();
    
    this.initialize();
  }

  async initialize() {
    try {
      await this.chromaService.initialize();
      console.log('Socket<PERSON>andler initialized successfully');
    } catch (error) {
      console.error('Failed to initialize SocketHandler:', error);
    }
  }

  handleConnection(socket) {
    console.log('Client connected:', socket.id);
    
    // Initialize client state
    this.clientStates.set(socket.id, {
      isTranscribing: false,
      currentMode: null,
      accumulatedTranscript: '',
      lastProcessedTime: Date.now(),
      currentEntity: null
    });

    // Handle mode selection
    socket.on('set-mode', (mode) => {
      const clientState = this.clientStates.get(socket.id);
      if (clientState) {
        clientState.currentMode = mode;
        clientState.currentEntity = null;
        clientState.accumulatedTranscript = '';
        console.log(`Client ${socket.id} set mode to: ${mode}`);
      }
    });

    // Handle transcription start
    socket.on('start-transcription', async () => {
      const clientState = this.clientStates.get(socket.id);
      if (!clientState || clientState.isTranscribing) return;

      try {
        clientState.isTranscribing = true;
        
        await this.deepgramService.startLiveTranscription(
          socket.id,
          (transcriptData) => this.handleTranscript(socket, transcriptData),
          (error) => this.handleTranscriptionError(socket, error)
        );

        socket.emit('transcription-started');
        console.log('Transcription started for client:', socket.id);

      } catch (error) {
        console.error('Failed to start transcription:', error);
        socket.emit('transcription-error', { error: error.message });
        clientState.isTranscribing = false;
      }
    });

    // Handle audio stream
    socket.on('audio-stream', (audioData) => {
      const clientState = this.clientStates.get(socket.id);
      if (clientState && clientState.isTranscribing) {
        this.deepgramService.sendAudio(socket.id, audioData);
      }
    });

    // Handle transcription stop
    socket.on('stop-transcription', () => {
      const clientState = this.clientStates.get(socket.id);
      if (clientState && clientState.isTranscribing) {
        this.deepgramService.stopTranscription(socket.id);
        clientState.isTranscribing = false;
        socket.emit('transcription-stopped');
        console.log('Transcription stopped for client:', socket.id);
      }
    });

    // Handle image generation
    socket.on('generate-image', async (data) => {
      try {
        const { entityId, entityData, entityType } = data;
        
        socket.emit('image-generation-started', { entityId });
        
        const imageUrl = await this.openaiService.generateImage(entityData, entityType);
        
        socket.emit('image-generated', { 
          entityId, 
          imageUrl,
          timestamp: Date.now()
        });

        console.log('Image generated for entity:', entityId);

      } catch (error) {
        console.error('Image generation failed:', error);
        socket.emit('image-generation-error', { 
          entityId: data.entityId, 
          error: error.message 
        });
      }
    });

    // Handle disconnect
    socket.on('disconnect', () => {
      console.log('Client disconnected:', socket.id);
      
      // Clean up client state
      const clientState = this.clientStates.get(socket.id);
      if (clientState && clientState.isTranscribing) {
        this.deepgramService.stopTranscription(socket.id);
      }
      
      this.clientStates.delete(socket.id);
    });
  }

  async handleTranscript(socket, transcriptData) {
    const clientState = this.clientStates.get(socket.id);
    if (!clientState) return;

    const { transcript, is_final } = transcriptData;
    
    // Send transcript to client for display
    socket.emit('transcript-update', {
      transcript,
      is_final,
      timestamp: Date.now()
    });

    // Accumulate transcript for processing
    if (is_final) {
      clientState.accumulatedTranscript += ' ' + transcript;
      
      // Process accumulated transcript every 2 seconds or after significant pauses
      const now = Date.now();
      if (now - clientState.lastProcessedTime > 2000) {
        await this.processAccumulatedTranscript(socket);
        clientState.lastProcessedTime = now;
      }
    }
  }

  async processAccumulatedTranscript(socket) {
    const clientState = this.clientStates.get(socket.id);
    if (!clientState || !clientState.accumulatedTranscript.trim()) return;

    const { currentMode, accumulatedTranscript } = clientState;
    if (!currentMode) return;

    try {
      // Get relevant context from ChromaDB
      const context = await this.chromaService.getContextForNarrative(
        accumulatedTranscript, 
        currentMode
      );

      let entityData;
      
      // Generate entity data based on mode
      switch (currentMode) {
        case 'character':
          entityData = await this.openaiService.generateCharacterData(
            accumulatedTranscript, 
            context
          );
          break;
        case 'location':
          entityData = await this.openaiService.generateLocationData(
            accumulatedTranscript, 
            context
          );
          break;
        case 'object':
          entityData = await this.openaiService.generateObjectData(
            accumulatedTranscript, 
            context
          );
          break;
        default:
          return;
      }

      // Generate unique entity ID
      const entityId = `${currentMode}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // Store current entity
      clientState.currentEntity = {
        id: entityId,
        type: currentMode,
        data: entityData,
        lastUpdated: Date.now()
      };

      // Send updated entity data to client
      socket.emit('entity-updated', {
        entityId,
        entityType: currentMode,
        entityData,
        sourceTranscript: accumulatedTranscript,
        timestamp: Date.now()
      });

      console.log(`Generated ${currentMode} data for client ${socket.id}`);

    } catch (error) {
      console.error('Failed to process transcript:', error);
      socket.emit('processing-error', { error: error.message });
    }
  }

  handleTranscriptionError(socket, error) {
    console.error('Transcription error for socket', socket.id, ':', error);
    socket.emit('transcription-error', { error: error.message });
    
    const clientState = this.clientStates.get(socket.id);
    if (clientState) {
      clientState.isTranscribing = false;
    }
  }

  async cleanup() {
    // Clean up all services
    this.deepgramService.cleanup();
    await this.chromaService.cleanup();
    this.clientStates.clear();
  }
}

module.exports = SocketHandler;
