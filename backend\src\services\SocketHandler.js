const DeepgramService = require('./DeepgramService');
const OpenAIService = require('./OpenAIService');
const ChromaDBService = require('./ChromaDBService');

class SocketHandler {
  constructor(io) {
    this.io = io;
    this.deepgramService = new DeepgramService();
    this.openaiService = new OpenAIService();
    this.chromaService = new ChromaDBService();
    
    // Store client states
    this.clientStates = new Map();
    
    this.initialize();
  }

  async initialize() {
    try {
      await this.chromaService.initialize();
      console.log('Socket<PERSON>andler initialized successfully');
    } catch (error) {
      console.error('Failed to initialize SocketHandler:', error);
    }
  }

  handleConnection(socket) {
    console.log('Client connected:', socket.id);
    
    // Initialize client state
    this.clientStates.set(socket.id, {
      isTranscribing: false,
      currentMode: null,
      accumulatedTranscript: '',
      lastProcessedTime: Date.now(),
      currentEntity: null
    });

    // Handle mode selection
    socket.on('set-mode', (mode) => {
      const clientState = this.clientStates.get(socket.id);
      if (clientState) {
        clientState.currentMode = mode;
        clientState.currentEntity = null;
        clientState.accumulatedTranscript = '';
        console.log(`Client ${socket.id} set mode to: ${mode}`);
      }
    });

    // Handle transcription start
    socket.on('start-transcription', async () => {
      const clientState = this.clientStates.get(socket.id);
      if (!clientState || clientState.isTranscribing) return;

      try {
        clientState.isTranscribing = true;
        
        await this.deepgramService.startLiveTranscription(
          socket.id,
          (transcriptData) => this.handleTranscript(socket, transcriptData),
          (error) => this.handleTranscriptionError(socket, error)
        );

        socket.emit('transcription-started');
        console.log('Transcription started for client:', socket.id);

      } catch (error) {
        console.error('Failed to start transcription:', error);
        socket.emit('transcription-error', { error: error.message });
        clientState.isTranscribing = false;
      }
    });

    // Handle audio stream
    socket.on('audio-stream', (audioData) => {
      const clientState = this.clientStates.get(socket.id);
      if (clientState && clientState.isTranscribing) {
        this.deepgramService.sendAudio(socket.id, audioData);
      }
    });

    // Handle transcription stop
    socket.on('stop-transcription', () => {
      const clientState = this.clientStates.get(socket.id);
      if (clientState && clientState.isTranscribing) {
        this.deepgramService.stopTranscription(socket.id);
        clientState.isTranscribing = false;
        socket.emit('transcription-stopped');
        console.log('Transcription stopped for client:', socket.id);
      }
    });

    // Handle image generation
    socket.on('generate-image', async (data) => {
      try {
        const { entityId, entityData, entityType } = data;
        
        socket.emit('image-generation-started', { entityId });
        
        const imageUrl = await this.openaiService.generateImage(entityData, entityType);
        
        socket.emit('image-generated', { 
          entityId, 
          imageUrl,
          timestamp: Date.now()
        });

        console.log('Image generated for entity:', entityId);

      } catch (error) {
        console.error('Image generation failed:', error);
        socket.emit('image-generation-error', { 
          entityId: data.entityId, 
          error: error.message 
        });
      }
    });

    // Handle disconnect
    socket.on('disconnect', () => {
      console.log('Client disconnected:', socket.id);
      
      // Clean up client state
      const clientState = this.clientStates.get(socket.id);
      if (clientState && clientState.isTranscribing) {
        this.deepgramService.stopTranscription(socket.id);
      }
      
      this.clientStates.delete(socket.id);
    });
  }

  async handleTranscript(socket, transcriptData) {
    const clientState = this.clientStates.get(socket.id);
    if (!clientState) return;

    const { transcript, is_final, confidence } = transcriptData;

    // Only process transcripts with reasonable confidence
    if (confidence && confidence < 0.6) {
      console.log(`Low confidence transcript ignored: ${transcript} (${confidence})`);
      return;
    }

    // Send transcript to client for display
    socket.emit('transcript-update', {
      transcript,
      is_final,
      confidence,
      timestamp: Date.now()
    });

    // Accumulate transcript for processing
    if (is_final && transcript.trim().length > 0) {
      // Clean up the transcript
      const cleanTranscript = transcript.trim();
      clientState.accumulatedTranscript += (clientState.accumulatedTranscript ? ' ' : '') + cleanTranscript;

      console.log(`Accumulated transcript: "${clientState.accumulatedTranscript}"`);

      // Process accumulated transcript with adaptive timing
      const now = Date.now();
      const timeSinceLastProcess = now - clientState.lastProcessedTime;
      const wordCount = clientState.accumulatedTranscript.trim().split(/\s+/).length;

      // Process if:
      // - It's been more than 3 seconds since last processing
      // - We have at least 15 words accumulated
      // - Or it's been more than 8 seconds regardless of word count
      const shouldProcess = timeSinceLastProcess > 3000 && wordCount >= 15 ||
                           timeSinceLastProcess > 8000;

      if (shouldProcess) {
        await this.processAccumulatedTranscript(socket);
        clientState.lastProcessedTime = now;
      }
    }
  }

  async processAccumulatedTranscript(socket) {
    const clientState = this.clientStates.get(socket.id);
    if (!clientState || !clientState.accumulatedTranscript.trim()) return;

    const { currentMode, accumulatedTranscript } = clientState;
    if (!currentMode) return;

    // Check if we have enough content to process (at least 10 words)
    const wordCount = accumulatedTranscript.trim().split(/\s+/).length;
    if (wordCount < 10) return;

    try {
      console.log(`Processing transcript for ${currentMode}: "${accumulatedTranscript.substring(0, 100)}..."`);

      // Get relevant context from ChromaDB
      const context = await this.chromaService.getContextForNarrative(
        accumulatedTranscript,
        currentMode
      );

      let entityData;

      // Generate entity data based on mode
      switch (currentMode) {
        case 'character':
          entityData = await this.openaiService.generateCharacterData(
            accumulatedTranscript,
            context
          );
          break;
        case 'location':
          entityData = await this.openaiService.generateLocationData(
            accumulatedTranscript,
            context
          );
          break;
        case 'object':
          entityData = await this.openaiService.generateObjectData(
            accumulatedTranscript,
            context
          );
          break;
        default:
          return;
      }

      // Generate unique entity ID or update existing one
      let entityId;
      if (clientState.currentEntity && clientState.currentEntity.type === currentMode) {
        // Update existing entity
        entityId = clientState.currentEntity.id;
        console.log(`Updating existing ${currentMode}: ${entityId}`);
      } else {
        // Create new entity
        entityId = `${currentMode}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        console.log(`Creating new ${currentMode}: ${entityId}`);
      }

      // Store current entity
      clientState.currentEntity = {
        id: entityId,
        type: currentMode,
        data: entityData,
        lastUpdated: Date.now(),
        sourceTranscript: accumulatedTranscript
      };

      // Send updated entity data to client
      socket.emit('entity-updated', {
        entityId,
        entityType: currentMode,
        entityData,
        sourceTranscript: accumulatedTranscript,
        timestamp: Date.now(),
        isUpdate: clientState.currentEntity && clientState.currentEntity.id === entityId
      });

      console.log(`Successfully generated ${currentMode} data for client ${socket.id}`);

      // Clear processed transcript to avoid reprocessing
      clientState.accumulatedTranscript = '';

    } catch (error) {
      console.error('Failed to process transcript:', error);
      socket.emit('processing-error', {
        error: error.message,
        transcript: accumulatedTranscript.substring(0, 100) + '...'
      });
    }
  }

  handleTranscriptionError(socket, error) {
    console.error('Transcription error for socket', socket.id, ':', error);
    socket.emit('transcription-error', { error: error.message });
    
    const clientState = this.clientStates.get(socket.id);
    if (clientState) {
      clientState.isTranscribing = false;
    }
  }

  async cleanup() {
    // Clean up all services
    this.deepgramService.cleanup();
    await this.chromaService.cleanup();
    this.clientStates.clear();
  }
}

module.exports = SocketHandler;
