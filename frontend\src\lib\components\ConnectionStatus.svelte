<script lang="ts">
  import { connectionStatus, isConnected } from '$lib/stores';

  $: statusColor = $isConnected ? '#4CAF50' : '#f44336';
  $: statusText = $connectionStatus === 'connected' ? 'Connected' : 
                  $connectionStatus === 'connecting' ? 'Connecting...' : 'Disconnected';
</script>

<div class="connection-status">
  <div class="status-indicator">
    <div 
      class="status-dot" 
      style="background-color: {statusColor}"
      class:pulse={$connectionStatus === 'connecting'}
    ></div>
    <span class="status-text">{statusText}</span>
  </div>
</div>

<style>
  .connection-status {
    display: flex;
    justify-content: center;
    margin-top: 1rem;
  }

  .status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 20px;
    font-size: 0.9rem;
  }

  .status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
  }

  .status-dot.pulse {
    animation: pulse-status 1.5s infinite;
  }

  .status-text {
    font-weight: 500;
  }

  @keyframes pulse-status {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.6; transform: scale(1.2); }
    100% { opacity: 1; transform: scale(1); }
  }
</style>
