<script lang="ts">
  import { isGeneratingImage } from '$lib/stores';
  import { socketService } from '$lib/socketService';
  import type { Entity } from '$lib/stores';
  import CharacterSheet from './sheets/CharacterSheet.svelte';
  import LocationSheet from './sheets/LocationSheet.svelte';
  import ObjectSheet from './sheets/ObjectSheet.svelte';

  export let entity: Entity;

  function generateImage() {
    if (!entity || $isGeneratingImage) return;
    socketService.generateImage(entity);
  }

  $: hasImage = entity?.data?.imageUrl;
  $: canGenerateImage = entity && !$isGeneratingImage;
</script>

<div class="entity-sheet">
  <div class="sheet-header">
    <div class="sheet-title">
      <span class="sheet-icon">
        {#if entity.type === 'character'}👤
        {:else if entity.type === 'location'}🏰
        {:else if entity.type === 'object'}⚔️
        {/if}
      </span>
      <h2>
        {entity.data.name || `Generated ${entity.type.charAt(0).toUpperCase() + entity.type.slice(1)}`}
      </h2>
    </div>
    
    <div class="sheet-actions">
      <button 
        class="generate-image-btn"
        class:loading={$isGeneratingImage}
        disabled={!canGenerateImage}
        on:click={generateImage}
        title="Generate image for this {entity.type}"
      >
        {#if $isGeneratingImage}
          <div class="spinner"></div>
          <span>Generating...</span>
        {:else}
          <span>🎨</span>
          <span>Generate Image</span>
        {/if}
      </button>
    </div>
  </div>

  <div class="sheet-content">
    {#if hasImage}
      <div class="entity-image">
        <img src={entity.data.imageUrl} alt="{entity.data.name || entity.type}" />
      </div>
    {/if}

    <div class="entity-details">
      {#if entity.type === 'character'}
        <CharacterSheet data={entity.data} />
      {:else if entity.type === 'location'}
        <LocationSheet data={entity.data} />
      {:else if entity.type === 'object'}
        <ObjectSheet data={entity.data} />
      {/if}
    </div>
  </div>

  <div class="sheet-footer">
    <div class="timestamp">
      Last updated: {new Date(entity.lastUpdated).toLocaleTimeString()}
    </div>
  </div>
</div>

<style>
  .entity-sheet {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;
  }

  .sheet-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .sheet-title {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .sheet-icon {
    font-size: 2rem;
  }

  .sheet-title h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
  }

  .generate-image-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: linear-gradient(135deg, #9C27B0, #7B1FA2);
    border: none;
    border-radius: 6px;
    color: white;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  .generate-image-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #AB47BC, #8E24AA);
    transform: translateY(-1px);
  }

  .generate-image-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .generate-image-btn.loading {
    background: linear-gradient(135deg, #666, #555);
  }

  .spinner {
    width: 1rem;
    height: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .sheet-content {
    padding: 1.5rem;
  }

  .entity-image {
    margin-bottom: 1.5rem;
    text-align: center;
  }

  .entity-image img {
    max-width: 100%;
    max-height: 300px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  .sheet-footer {
    padding: 1rem 1.5rem;
    background: rgba(0, 0, 0, 0.2);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }

  .timestamp {
    font-size: 0.8rem;
    opacity: 0.7;
    text-align: center;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @media (max-width: 768px) {
    .sheet-header {
      flex-direction: column;
      gap: 1rem;
      align-items: stretch;
    }
    
    .sheet-title {
      justify-content: center;
    }
    
    .generate-image-btn {
      justify-content: center;
    }
  }
</style>
