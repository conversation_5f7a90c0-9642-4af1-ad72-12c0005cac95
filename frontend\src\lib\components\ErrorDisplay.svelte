<script lang="ts">
  import { lastError, clearError } from '$lib/stores';
  import { onMount } from 'svelte';

  let errorElement: HTMLDivElement;
  let isVisible = false;

  $: if ($lastError) {
    isVisible = true;
  }

  function handleClose() {
    isVisible = false;
    setTimeout(() => {
      clearError();
    }, 300); // Wait for animation to complete
  }

  // Auto-hide after 5 seconds
  $: if ($lastError && isVisible) {
    const timer = setTimeout(() => {
      handleClose();
    }, 5000);
  }
</script>

{#if $lastError}
  <div 
    class="error-overlay" 
    class:visible={isVisible}
    on:click={handleClose}
    role="alert"
    aria-live="polite"
  >
    <div 
      class="error-message"
      bind:this={errorElement}
      on:click|stopPropagation
    >
      <div class="error-header">
        <span class="error-icon">⚠️</span>
        <span class="error-title">Error</span>
        <button 
          class="close-button"
          on:click={handleClose}
          aria-label="Close error message"
        >
          ✕
        </button>
      </div>
      
      <div class="error-content">
        <p>{$lastError}</p>
      </div>
      
      <div class="error-actions">
        <button class="dismiss-button" on:click={handleClose}>
          Dismiss
        </button>
      </div>
    </div>
  </div>
{/if}

<style>
  .error-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(4px);
  }

  .error-overlay.visible {
    opacity: 1;
    visibility: visible;
  }

  .error-message {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    color: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
  }

  .error-overlay.visible .error-message {
    transform: scale(1);
  }

  .error-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1.5rem 1.5rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  }

  .error-icon {
    font-size: 1.5rem;
  }

  .error-title {
    font-size: 1.2rem;
    font-weight: 600;
    flex: 1;
  }

  .close-button {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    line-height: 1;
  }

  .close-button:hover {
    background: rgba(255, 255, 255, 0.2);
  }

  .error-content {
    padding: 1rem 1.5rem;
    line-height: 1.5;
  }

  .error-content p {
    margin: 0;
    font-size: 1rem;
  }

  .error-actions {
    padding: 1rem 1.5rem 1.5rem;
    display: flex;
    justify-content: flex-end;
  }

  .dismiss-button {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .dismiss-button:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
  }

  @media (max-width: 768px) {
    .error-message {
      width: 95%;
      margin: 1rem;
    }
    
    .error-header {
      padding: 1rem 1rem 0.75rem;
    }
    
    .error-content {
      padding: 0.75rem 1rem;
    }
    
    .error-actions {
      padding: 0.75rem 1rem 1rem;
    }
  }
</style>
