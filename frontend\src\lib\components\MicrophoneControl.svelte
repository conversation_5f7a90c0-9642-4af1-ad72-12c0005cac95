<script lang="ts">
  import { isTranscribing, isConnected } from '$lib/stores';
  import { socketService } from '$lib/socketService';

  let isLoading = false;

  async function toggleTranscription() {
    if (isLoading) return;
    
    isLoading = true;
    
    try {
      if ($isTranscribing) {
        socketService.stopTranscription();
      } else {
        await socketService.startTranscription();
      }
    } catch (error) {
      console.error('Failed to toggle transcription:', error);
    } finally {
      isLoading = false;
    }
  }

  $: buttonText = $isTranscribing ? 'Stop Listening' : 'Start Listening';
  $: buttonIcon = $isTranscribing ? '🛑' : '🎤';
  $: isDisabled = !$isConnected || isLoading;
</script>

<div class="microphone-control">
  <h3 class="control-title">Voice Control</h3>
  
  <button
    class="mic-button"
    class:active={$isTranscribing}
    class:disabled={isDisabled}
    disabled={isDisabled}
    on:click={toggleTranscription}
  >
    <div class="mic-icon">
      {#if isLoading}
        <div class="spinner"></div>
      {:else}
        <span class="icon">{buttonIcon}</span>
      {/if}
    </div>
    <span class="mic-text">{buttonText}</span>
  </button>

  <div class="status-indicators">
    <div class="status-item">
      <span class="status-dot" class:connected={$isConnected}></span>
      <span class="status-label">
        {$isConnected ? 'Connected' : 'Disconnected'}
      </span>
    </div>
    
    {#if $isTranscribing}
      <div class="status-item">
        <div class="recording-indicator">
          <div class="pulse"></div>
        </div>
        <span class="status-label">Recording</span>
      </div>
    {/if}
  </div>

  <div class="instructions">
    <p>Click the microphone to start voice recognition. Speak naturally about your game scenario.</p>
  </div>
</div>

<style>
  .microphone-control {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
  }

  .control-title {
    margin: 0 0 1.5rem 0;
    font-size: 1.2rem;
    font-weight: 600;
  }

  .mic-button {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, 0.3);
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin: 0 auto 1.5rem;
    font-size: 0.9rem;
    font-weight: 500;
  }

  .mic-button:hover:not(.disabled) {
    transform: scale(1.05);
    box-shadow: 0 0 30px rgba(76, 175, 80, 0.4);
  }

  .mic-button.active {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    animation: pulse 2s infinite;
  }

  .mic-button.disabled {
    background: rgba(128, 128, 128, 0.5);
    cursor: not-allowed;
    opacity: 0.6;
  }

  .mic-icon {
    font-size: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
  }

  .spinner {
    width: 2rem;
    height: 2rem;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .status-indicators {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
  }

  .status-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
  }

  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #f44336;
    transition: background-color 0.3s ease;
  }

  .status-dot.connected {
    background: #4CAF50;
  }

  .recording-indicator {
    position: relative;
    width: 8px;
    height: 8px;
  }

  .pulse {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #f44336;
    animation: pulse-dot 1.5s infinite;
  }

  .instructions {
    font-size: 0.85rem;
    opacity: 0.8;
    line-height: 1.4;
  }

  .instructions p {
    margin: 0;
  }

  @keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(244, 67, 54, 0.7); }
    70% { box-shadow: 0 0 0 20px rgba(244, 67, 54, 0); }
    100% { box-shadow: 0 0 0 0 rgba(244, 67, 54, 0); }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @keyframes pulse-dot {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.5); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
  }

  @media (max-width: 768px) {
    .mic-button {
      width: 100px;
      height: 100px;
      font-size: 0.8rem;
    }
    
    .mic-icon {
      font-size: 1.5rem;
      width: 2.5rem;
      height: 2.5rem;
    }
  }
</style>
