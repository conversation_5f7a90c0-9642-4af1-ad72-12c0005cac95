<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { currentMode } from '$lib/stores';
  import type { AppMode } from '$lib/stores';

  const dispatch = createEventDispatcher<{ modeChange: string }>();

  const modes: { value: AppMode; label: string; icon: string; description: string }[] = [
    {
      value: 'character',
      label: 'Character',
      icon: '👤',
      description: 'Generate NPCs and characters'
    },
    {
      value: 'location',
      label: 'Location',
      icon: '🏰',
      description: 'Create places and environments'
    },
    {
      value: 'object',
      label: 'Object',
      icon: '⚔️',
      description: 'Design items and equipment'
    }
  ];

  function selectMode(mode: AppMode) {
    currentMode.set(mode);
    dispatch('modeChange', mode);
  }
</script>

<div class="mode-selector">
  <h3 class="selector-title">Generation Mode</h3>
  <div class="mode-buttons">
    {#each modes as mode}
      <button
        class="mode-button"
        class:active={$currentMode === mode.value}
        on:click={() => selectMode(mode.value)}
        title={mode.description}
      >
        <span class="mode-icon">{mode.icon}</span>
        <span class="mode-label">{mode.label}</span>
      </button>
    {/each}
  </div>
</div>

<style>
  .mode-selector {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .selector-title {
    margin: 0 0 1rem 0;
    font-size: 1.2rem;
    font-weight: 600;
    text-align: center;
  }

  .mode-buttons {
    display: flex;
    gap: 0.5rem;
    flex-direction: column;
  }

  .mode-button {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: white;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
  }

  .mode-button:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
  }

  .mode-button.active {
    background: rgba(255, 255, 255, 0.3);
    border-color: #4CAF50;
    box-shadow: 0 0 20px rgba(76, 175, 80, 0.3);
  }

  .mode-icon {
    font-size: 1.5rem;
    min-width: 2rem;
    text-align: center;
  }

  .mode-label {
    font-weight: 500;
    flex: 1;
  }

  @media (max-width: 768px) {
    .mode-buttons {
      flex-direction: row;
    }
    
    .mode-button {
      flex: 1;
      flex-direction: column;
      gap: 0.5rem;
      padding: 0.75rem;
    }
    
    .mode-label {
      font-size: 0.9rem;
    }
  }
</style>
