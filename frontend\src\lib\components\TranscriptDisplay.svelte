<script lang="ts">
  import { displayTranscript, currentTranscript, isTranscribing } from '$lib/stores';
  import { onMount } from 'svelte';

  let transcriptContainer: HTMLDivElement;
  let autoScroll = true;

  // Auto-scroll to bottom when new transcript arrives
  $: if (transcriptContainer && $displayTranscript && autoScroll) {
    setTimeout(() => {
      transcriptContainer.scrollTop = transcriptContainer.scrollHeight;
    }, 10);
  }

  function handleScroll() {
    if (!transcriptContainer) return;
    
    const { scrollTop, scrollHeight, clientHeight } = transcriptContainer;
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;
    autoScroll = isAtBottom;
  }

  function clearTranscript() {
    // This would need to be implemented in the stores
    // For now, just a placeholder
    console.log('Clear transcript requested');
  }
</script>

<div class="transcript-display">
  <div class="transcript-header">
    <h3 class="transcript-title">Live Transcript</h3>
    <div class="transcript-controls">
      {#if $isTranscribing}
        <div class="live-indicator">
          <div class="live-dot"></div>
          <span>LIVE</span>
        </div>
      {/if}
      <button 
        class="clear-button" 
        on:click={clearTranscript}
        title="Clear transcript"
      >
        🗑️
      </button>
    </div>
  </div>

  <div 
    class="transcript-content"
    bind:this={transcriptContainer}
    on:scroll={handleScroll}
  >
    {#if $displayTranscript.trim()}
      <div class="transcript-text">
        <!-- Final transcript -->
        <span class="final-text">{$displayTranscript}</span>
        
        <!-- Current interim transcript -->
        {#if $currentTranscript.trim()}
          <span class="interim-text">{$currentTranscript}</span>
        {/if}
      </div>
    {:else}
      <div class="empty-transcript">
        <p>Transcript will appear here when you start speaking...</p>
      </div>
    {/if}
  </div>

  <div class="transcript-footer">
    <div class="scroll-indicator" class:hidden={autoScroll}>
      <button class="scroll-to-bottom" on:click={() => {
        transcriptContainer.scrollTop = transcriptContainer.scrollHeight;
        autoScroll = true;
      }}>
        ↓ Scroll to bottom
      </button>
    </div>
  </div>
</div>

<style>
  .transcript-display {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
    height: 300px;
  }

  .transcript-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem 0.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .transcript-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
  }

  .transcript-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .live-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    font-weight: 600;
    color: #f44336;
  }

  .live-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #f44336;
    animation: pulse-live 1.5s infinite;
  }

  .clear-button {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  .clear-button:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
  }

  .transcript-content {
    flex: 1;
    padding: 1rem 1.5rem;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.5;
  }

  .transcript-content::-webkit-scrollbar {
    width: 6px;
  }

  .transcript-content::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  .transcript-content::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
  }

  .transcript-content::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
  }

  .transcript-text {
    word-wrap: break-word;
  }

  .final-text {
    color: white;
  }

  .interim-text {
    color: rgba(255, 255, 255, 0.7);
    font-style: italic;
  }

  .empty-transcript {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    opacity: 0.6;
    text-align: center;
  }

  .empty-transcript p {
    margin: 0;
    font-style: italic;
  }

  .transcript-footer {
    padding: 0.5rem 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }

  .scroll-indicator {
    text-align: center;
    transition: opacity 0.3s ease;
  }

  .scroll-indicator.hidden {
    opacity: 0;
    pointer-events: none;
  }

  .scroll-to-bottom {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .scroll-to-bottom:hover {
    background: rgba(255, 255, 255, 0.2);
  }

  @keyframes pulse-live {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.2); }
    100% { opacity: 1; transform: scale(1); }
  }

  @media (max-width: 768px) {
    .transcript-display {
      height: 200px;
    }
    
    .transcript-content {
      font-size: 0.8rem;
      padding: 0.75rem 1rem;
    }
    
    .transcript-header {
      padding: 0.75rem 1rem 0.5rem;
    }
  }
</style>
