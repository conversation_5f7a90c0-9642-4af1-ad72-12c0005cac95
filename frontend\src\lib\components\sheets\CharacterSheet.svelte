<script lang="ts">
  import type { CharacterData } from '$lib/stores';

  export let data: CharacterData;

  function formatList(items: string[]): string {
    if (!items || items.length === 0) return 'None';
    return items.join(', ');
  }
</script>

<div class="character-sheet">
  <div class="sheet-grid">
    <!-- Basic Info -->
    <div class="info-section">
      <h3>Basic Information</h3>
      <div class="info-grid">
        <div class="info-item">
          <label>Race:</label>
          <span>{data.race || 'Unknown'}</span>
        </div>
        <div class="info-item">
          <label>Class:</label>
          <span>{data.class || 'Unknown'}</span>
        </div>
      </div>
    </div>

    <!-- Appearance -->
    <div class="info-section">
      <h3>Appearance</h3>
      <div class="info-grid">
        <div class="info-item">
          <label>Size:</label>
          <span>{data.appearance.size || 'Medium'}</span>
        </div>
        <div class="info-item full-width">
          <label>Description:</label>
          <p>{data.appearance.description || 'No description available'}</p>
        </div>
        {#if data.appearance.notable_features.length > 0}
          <div class="info-item full-width">
            <label>Notable Features:</label>
            <ul>
              {#each data.appearance.notable_features as feature}
                <li>{feature}</li>
              {/each}
            </ul>
          </div>
        {/if}
      </div>
    </div>

    <!-- Personality -->
    <div class="info-section">
      <h3>Personality</h3>
      <div class="info-grid">
        <div class="info-item">
          <label>Demeanor:</label>
          <span>{data.personality.demeanor || 'Unknown'}</span>
        </div>
        {#if data.personality.traits.length > 0}
          <div class="info-item full-width">
            <label>Traits:</label>
            <div class="trait-tags">
              {#each data.personality.traits as trait}
                <span class="trait-tag">{trait}</span>
              {/each}
            </div>
          </div>
        {/if}
      </div>
    </div>

    <!-- Background -->
    <div class="info-section">
      <h3>Background</h3>
      <div class="info-grid">
        <div class="info-item">
          <label>Occupation:</label>
          <span>{data.background.occupation || 'Unknown'}</span>
        </div>
        <div class="info-item">
          <label>Origin:</label>
          <span>{data.background.origin || 'Unknown'}</span>
        </div>
      </div>
    </div>

    <!-- Abilities -->
    <div class="info-section">
      <h3>Abilities</h3>
      <div class="info-grid">
        {#if data.abilities.suggested_stats}
          <div class="info-item full-width">
            <label>Suggested Stats:</label>
            <p>{data.abilities.suggested_stats}</p>
          </div>
        {/if}
        {#if data.abilities.special_abilities.length > 0}
          <div class="info-item full-width">
            <label>Special Abilities:</label>
            <ul>
              {#each data.abilities.special_abilities as ability}
                <li>{ability}</li>
              {/each}
            </ul>
          </div>
        {/if}
      </div>
    </div>

    <!-- Equipment -->
    {#if data.equipment.length > 0}
      <div class="info-section">
        <h3>Equipment</h3>
        <div class="equipment-list">
          {#each data.equipment as item}
            <span class="equipment-item">{item}</span>
          {/each}
        </div>
      </div>
    {/if}

    <!-- Notes -->
    {#if data.notes}
      <div class="info-section full-width">
        <h3>Notes</h3>
        <p class="notes-text">{data.notes}</p>
      </div>
    {/if}
  </div>
</div>

<style>
  .character-sheet {
    color: white;
  }

  .sheet-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }

  .info-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .info-section.full-width {
    grid-column: 1 / -1;
  }

  .info-section h3 {
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #4CAF50;
    border-bottom: 1px solid rgba(76, 175, 80, 0.3);
    padding-bottom: 0.5rem;
  }

  .info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .info-item.full-width {
    grid-column: 1 / -1;
  }

  .info-item label {
    font-weight: 600;
    font-size: 0.9rem;
    opacity: 0.8;
  }

  .info-item span {
    font-size: 1rem;
  }

  .info-item p {
    margin: 0;
    line-height: 1.4;
    font-size: 0.95rem;
  }

  .info-item ul {
    margin: 0;
    padding-left: 1.2rem;
    line-height: 1.4;
  }

  .info-item li {
    font-size: 0.95rem;
    margin-bottom: 0.25rem;
  }

  .trait-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .trait-tag {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.85rem;
    font-weight: 500;
    border: 1px solid rgba(76, 175, 80, 0.3);
  }

  .equipment-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .equipment-item {
    background: rgba(255, 193, 7, 0.2);
    color: #FFC107;
    padding: 0.25rem 0.75rem;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 500;
    border: 1px solid rgba(255, 193, 7, 0.3);
  }

  .notes-text {
    background: rgba(255, 255, 255, 0.05);
    padding: 1rem;
    border-radius: 6px;
    border-left: 3px solid #2196F3;
    margin: 0;
    line-height: 1.5;
    font-style: italic;
  }

  @media (max-width: 768px) {
    .sheet-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
    
    .info-section {
      padding: 0.75rem;
    }
    
    .info-section h3 {
      font-size: 1rem;
    }
  }
</style>
