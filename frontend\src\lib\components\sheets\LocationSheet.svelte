<script lang="ts">
  import type { LocationData } from '$lib/stores';

  export let data: LocationData;
</script>

<div class="location-sheet">
  <div class="sheet-grid">
    <!-- Basic Info -->
    <div class="info-section">
      <h3>Basic Information</h3>
      <div class="info-grid">
        <div class="info-item">
          <label>Type:</label>
          <span>{data.type || 'Unknown'}</span>
        </div>
        <div class="info-item full-width">
          <label>Description:</label>
          <p>{data.description || 'No description available'}</p>
        </div>
        <div class="info-item full-width">
          <label>Atmosphere:</label>
          <p class="atmosphere-text">{data.atmosphere || 'Unknown atmosphere'}</p>
        </div>
      </div>
    </div>

    <!-- Features -->
    <div class="info-section">
      <h3>Features</h3>
      <div class="info-grid">
        {#if data.features.notable_landmarks.length > 0}
          <div class="info-item">
            <label>Notable Landmarks:</label>
            <ul>
              {#each data.features.notable_landmarks as landmark}
                <li>{landmark}</li>
              {/each}
            </ul>
          </div>
        {/if}
        
        {#if data.features.hazards.length > 0}
          <div class="info-item">
            <label>Hazards:</label>
            <ul class="hazard-list">
              {#each data.features.hazards as hazard}
                <li>{hazard}</li>
              {/each}
            </ul>
          </div>
        {/if}
        
        {#if data.features.resources.length > 0}
          <div class="info-item">
            <label>Resources:</label>
            <ul class="resource-list">
              {#each data.features.resources as resource}
                <li>{resource}</li>
              {/each}
            </ul>
          </div>
        {/if}
      </div>
    </div>

    <!-- Inhabitants -->
    {#if data.inhabitants.length > 0}
      <div class="info-section">
        <h3>Inhabitants</h3>
        <div class="inhabitant-tags">
          {#each data.inhabitants as inhabitant}
            <span class="inhabitant-tag">{inhabitant}</span>
          {/each}
        </div>
      </div>
    {/if}

    <!-- Connections -->
    {#if data.connections.length > 0}
      <div class="info-section">
        <h3>Connections</h3>
        <div class="connection-list">
          {#each data.connections as connection}
            <div class="connection-item">
              <span class="connection-icon">🔗</span>
              <span>{connection}</span>
            </div>
          {/each}
        </div>
      </div>
    {/if}

    <!-- Secrets -->
    {#if data.secrets.length > 0}
      <div class="info-section secrets-section">
        <h3>Secrets</h3>
        <div class="secrets-list">
          {#each data.secrets as secret}
            <div class="secret-item">
              <span class="secret-icon">🤫</span>
              <span>{secret}</span>
            </div>
          {/each}
        </div>
      </div>
    {/if}

    <!-- Notes -->
    {#if data.notes}
      <div class="info-section full-width">
        <h3>Notes</h3>
        <p class="notes-text">{data.notes}</p>
      </div>
    {/if}
  </div>
</div>

<style>
  .location-sheet {
    color: white;
  }

  .sheet-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }

  .info-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .info-section.full-width {
    grid-column: 1 / -1;
  }

  .info-section h3 {
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #FF9800;
    border-bottom: 1px solid rgba(255, 152, 0, 0.3);
    padding-bottom: 0.5rem;
  }

  .secrets-section h3 {
    color: #9C27B0;
    border-bottom-color: rgba(156, 39, 176, 0.3);
  }

  .info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .info-item.full-width {
    grid-column: 1 / -1;
  }

  .info-item label {
    font-weight: 600;
    font-size: 0.9rem;
    opacity: 0.8;
  }

  .info-item span {
    font-size: 1rem;
  }

  .info-item p {
    margin: 0;
    line-height: 1.4;
    font-size: 0.95rem;
  }

  .atmosphere-text {
    background: rgba(255, 152, 0, 0.1);
    padding: 0.75rem;
    border-radius: 6px;
    border-left: 3px solid #FF9800;
    font-style: italic;
  }

  .info-item ul {
    margin: 0;
    padding-left: 1.2rem;
    line-height: 1.4;
  }

  .info-item li {
    font-size: 0.95rem;
    margin-bottom: 0.25rem;
  }

  .hazard-list li {
    color: #f44336;
  }

  .resource-list li {
    color: #4CAF50;
  }

  .inhabitant-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .inhabitant-tag {
    background: rgba(33, 150, 243, 0.2);
    color: #2196F3;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.85rem;
    font-weight: 500;
    border: 1px solid rgba(33, 150, 243, 0.3);
  }

  .connection-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .connection-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    font-size: 0.9rem;
  }

  .connection-icon {
    opacity: 0.7;
  }

  .secrets-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .secret-item {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    padding: 0.75rem;
    background: rgba(156, 39, 176, 0.1);
    border-radius: 6px;
    border: 1px solid rgba(156, 39, 176, 0.2);
    font-size: 0.9rem;
    line-height: 1.4;
  }

  .secret-icon {
    opacity: 0.7;
    margin-top: 0.1rem;
  }

  .notes-text {
    background: rgba(255, 255, 255, 0.05);
    padding: 1rem;
    border-radius: 6px;
    border-left: 3px solid #2196F3;
    margin: 0;
    line-height: 1.5;
    font-style: italic;
  }

  @media (max-width: 768px) {
    .sheet-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
    
    .info-section {
      padding: 0.75rem;
    }
    
    .info-section h3 {
      font-size: 1rem;
    }
    
    .inhabitant-tags {
      justify-content: center;
    }
  }
</style>
