<script lang="ts">
  import type { ObjectData } from '$lib/stores';

  export let data: ObjectData;

  function getRarityColor(rarity: string | null): string {
    if (!rarity) return '#666';
    
    switch (rarity.toLowerCase()) {
      case 'common': return '#9E9E9E';
      case 'uncommon': return '#4CAF50';
      case 'rare': return '#2196F3';
      case 'very rare': return '#9C27B0';
      case 'legendary': return '#FF9800';
      case 'artifact': return '#f44336';
      default: return '#666';
    }
  }

  $: rarityColor = getRarityColor(data.value.rarity);
</script>

<div class="object-sheet">
  <div class="sheet-grid">
    <!-- Basic Info -->
    <div class="info-section">
      <h3>Basic Information</h3>
      <div class="info-grid">
        <div class="info-item">
          <label>Type:</label>
          <span>{data.type || 'Unknown'}</span>
        </div>
        <div class="info-item full-width">
          <label>Description:</label>
          <p>{data.description || 'No description available'}</p>
        </div>
      </div>
    </div>

    <!-- Properties -->
    <div class="info-section">
      <h3>Properties</h3>
      <div class="info-grid">
        <div class="info-item">
          <label>Material:</label>
          <span>{data.properties.material || 'Unknown'}</span>
        </div>
        <div class="info-item">
          <label>Size:</label>
          <span>{data.properties.size || 'Unknown'}</span>
        </div>
        <div class="info-item">
          <label>Weight:</label>
          <span>{data.properties.weight || 'Unknown'}</span>
        </div>
        <div class="info-item">
          <label>Condition:</label>
          <span>{data.properties.condition || 'Unknown'}</span>
        </div>
      </div>
    </div>

    <!-- Mechanical Effects -->
    <div class="info-section">
      <h3>Mechanical Effects</h3>
      <div class="info-grid">
        {#if data.mechanical_effects.damage}
          <div class="info-item">
            <label>Damage:</label>
            <span class="damage-value">{data.mechanical_effects.damage}</span>
          </div>
        {/if}
        
        {#if data.mechanical_effects.armor_value}
          <div class="info-item">
            <label>Armor Value:</label>
            <span class="armor-value">{data.mechanical_effects.armor_value}</span>
          </div>
        {/if}
        
        {#if data.mechanical_effects.special_properties.length > 0}
          <div class="info-item full-width">
            <label>Special Properties:</label>
            <div class="special-properties">
              {#each data.mechanical_effects.special_properties as property}
                <span class="special-property">{property}</span>
              {/each}
            </div>
          </div>
        {/if}
      </div>
    </div>

    <!-- Value -->
    <div class="info-section">
      <h3>Value</h3>
      <div class="info-grid">
        {#if data.value.estimated_cost}
          <div class="info-item">
            <label>Estimated Cost:</label>
            <span class="cost-value">{data.value.estimated_cost}</span>
          </div>
        {/if}
        
        {#if data.value.rarity}
          <div class="info-item">
            <label>Rarity:</label>
            <span 
              class="rarity-badge" 
              style="background-color: {rarityColor}20; color: {rarityColor}; border-color: {rarityColor}40"
            >
              {data.value.rarity}
            </span>
          </div>
        {/if}
      </div>
    </div>

    <!-- History -->
    {#if data.history}
      <div class="info-section full-width">
        <h3>History</h3>
        <p class="history-text">{data.history}</p>
      </div>
    {/if}

    <!-- Notes -->
    {#if data.notes}
      <div class="info-section full-width">
        <h3>Notes</h3>
        <p class="notes-text">{data.notes}</p>
      </div>
    {/if}
  </div>
</div>

<style>
  .object-sheet {
    color: white;
  }

  .sheet-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }

  .info-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .info-section.full-width {
    grid-column: 1 / -1;
  }

  .info-section h3 {
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #FFC107;
    border-bottom: 1px solid rgba(255, 193, 7, 0.3);
    padding-bottom: 0.5rem;
  }

  .info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
  }

  .info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .info-item.full-width {
    grid-column: 1 / -1;
  }

  .info-item label {
    font-weight: 600;
    font-size: 0.9rem;
    opacity: 0.8;
  }

  .info-item span {
    font-size: 1rem;
  }

  .info-item p {
    margin: 0;
    line-height: 1.4;
    font-size: 0.95rem;
  }

  .damage-value {
    color: #f44336;
    font-weight: 600;
  }

  .armor-value {
    color: #2196F3;
    font-weight: 600;
  }

  .cost-value {
    color: #4CAF50;
    font-weight: 600;
  }

  .rarity-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.85rem;
    font-weight: 600;
    border: 1px solid;
    text-transform: capitalize;
    display: inline-block;
  }

  .special-properties {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .special-property {
    background: rgba(156, 39, 176, 0.2);
    color: #E1BEE7;
    padding: 0.25rem 0.75rem;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 500;
    border: 1px solid rgba(156, 39, 176, 0.3);
  }

  .history-text {
    background: rgba(255, 193, 7, 0.1);
    padding: 1rem;
    border-radius: 6px;
    border-left: 3px solid #FFC107;
    margin: 0;
    line-height: 1.5;
    font-style: italic;
  }

  .notes-text {
    background: rgba(255, 255, 255, 0.05);
    padding: 1rem;
    border-radius: 6px;
    border-left: 3px solid #2196F3;
    margin: 0;
    line-height: 1.5;
    font-style: italic;
  }

  @media (max-width: 768px) {
    .sheet-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
    
    .info-grid {
      grid-template-columns: 1fr;
    }
    
    .info-section {
      padding: 0.75rem;
    }
    
    .info-section h3 {
      font-size: 1rem;
    }
    
    .special-properties {
      justify-content: center;
    }
  }
</style>
