import { io, type Socket } from 'socket.io-client';
import {
    accumulatedTranscript,
    currentEntity,
    currentTranscript,
    isConnected,
    isGeneratingImage,
    isTranscribing,
    setError,
    socket as socketStore,
    type AppMode,
    type Entity
} from './stores';

class SocketService {
  private socket: Socket | null = null;
  private mediaRecorder: MediaRecorder | null = null;
  private audioStream: MediaStream | null = null;

  async connect(serverUrl: string = 'http://localhost:3001') {
    try {
      this.socket = io(serverUrl, {
        transports: ['websocket', 'polling']
      });

      this.setupEventListeners();
      socketStore.set(this.socket);

      return new Promise<void>((resolve, reject) => {
        if (!this.socket) return reject(new Error('Socket not initialized'));

        this.socket.on('connect', () => {
          console.log('Connected to server');
          isConnected.set(true);
          resolve();
        });

        this.socket.on('connect_error', (error) => {
          console.error('Connection error:', error);
          isConnected.set(false);
          setError('Failed to connect to server');
          reject(error);
        });

        // Set timeout for connection
        setTimeout(() => {
          if (!this.socket?.connected) {
            reject(new Error('Connection timeout'));
          }
        }, 5000);
      });

    } catch (error) {
      console.error('Failed to connect:', error);
      setError('Failed to connect to server');
      throw error;
    }
  }

  private setupEventListeners() {
    if (!this.socket) return;

    // Connection events
    this.socket.on('disconnect', () => {
      console.log('Disconnected from server');
      isConnected.set(false);
    });

    // Transcription events
    this.socket.on('transcription-started', () => {
      console.log('Transcription started');
      isTranscribing.set(true);
    });

    this.socket.on('transcription-stopped', () => {
      console.log('Transcription stopped');
      isTranscribing.set(false);
    });

    this.socket.on('transcript-update', (data) => {
      const { transcript, is_final } = data;
      
      if (is_final) {
        // Add to accumulated transcript
        accumulatedTranscript.update(current => current + ' ' + transcript);
        currentTranscript.set('');
      } else {
        // Update current (interim) transcript
        currentTranscript.set(transcript);
      }
    });

    // Entity generation events
    this.socket.on('entity-updated', (data) => {
      const { entityId, entityType, entityData, sourceTranscript, timestamp } = data;
      
      const entity: Entity = {
        id: entityId,
        type: entityType as AppMode,
        data: entityData,
        lastUpdated: timestamp
      };

      currentEntity.set(entity);
      console.log('Entity updated:', entity);
    });

    // Image generation events
    this.socket.on('image-generation-started', (data) => {
      console.log('Image generation started for:', data.entityId);
      isGeneratingImage.set(true);
    });

    this.socket.on('image-generated', (data) => {
      const { entityId, imageUrl } = data;
      console.log('Image generated:', imageUrl);
      
      // Update current entity with image
      currentEntity.update(entity => {
        if (entity && entity.id === entityId) {
          return {
            ...entity,
            data: {
              ...entity.data,
              imageUrl
            }
          };
        }
        return entity;
      });

      isGeneratingImage.set(false);
    });

    this.socket.on('image-generation-error', (data) => {
      console.error('Image generation failed:', data.error);
      setError(`Image generation failed: ${data.error}`);
      isGeneratingImage.set(false);
    });

    // Error events
    this.socket.on('transcription-error', (data) => {
      console.error('Transcription error:', data.error);
      setError(`Transcription error: ${data.error}`);
      isTranscribing.set(false);
    });

    this.socket.on('processing-error', (data) => {
      console.error('Processing error:', data.error);
      setError(`Processing error: ${data.error}`);
    });
  }

  async setMode(mode: AppMode) {
    if (!this.socket?.connected) {
      throw new Error('Not connected to server');
    }

    this.socket.emit('set-mode', mode);
    console.log('Mode set to:', mode);
  }

  async startTranscription() {
    if (!this.socket?.connected) {
      throw new Error('Not connected to server');
    }

    try {
      // Request microphone access with optimized settings
      this.audioStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 48000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          googEchoCancellation: true,
          googAutoGainControl: true,
          googNoiseSuppression: true,
          googHighpassFilter: true,
          googTypingNoiseDetection: true
        }
      });

      // Check for supported MIME types (prioritize formats compatible with Deepgram)
      const mimeTypes = [
        'audio/webm;codecs=pcm',
        'audio/wav',
        'audio/webm;codecs=opus',
        'audio/webm',
        'audio/mp4',
        'audio/ogg;codecs=opus'
      ];

      let selectedMimeType = '';
      for (const mimeType of mimeTypes) {
        if (MediaRecorder.isTypeSupported(mimeType)) {
          selectedMimeType = mimeType;
          break;
        }
      }

      if (!selectedMimeType) {
        throw new Error('No supported audio format found');
      }

      console.log('Using audio format:', selectedMimeType);

      // Create MediaRecorder with optimal settings for Deepgram
      this.mediaRecorder = new MediaRecorder(this.audioStream, {
        mimeType: selectedMimeType,
        audioBitsPerSecond: 128000
      });

      // Handle audio data
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0 && this.socket?.connected) {
          // Convert blob to array buffer and send
          event.data.arrayBuffer().then(buffer => {
            if (this.socket?.connected && buffer.byteLength > 0) {
              this.socket.emit('audio-stream', buffer);
            }
          }).catch(error => {
            console.error('Error processing audio data:', error);
          });
        }
      };

      // Handle recording errors
      this.mediaRecorder.onerror = (event) => {
        console.error('MediaRecorder error:', event);
        setError('Audio recording error occurred');
      };

      // Start recording with smaller chunks for better real-time performance
      this.mediaRecorder.start(250); // Send data every 250ms

      // Notify server to start transcription
      this.socket.emit('start-transcription');

      console.log('Audio transcription started');

    } catch (error) {
      console.error('Failed to start transcription:', error);

      if (error.name === 'NotAllowedError') {
        setError('Microphone access denied. Please allow microphone access and try again.');
      } else if (error.name === 'NotFoundError') {
        setError('No microphone found. Please connect a microphone and try again.');
      } else {
        setError(`Failed to access microphone: ${error.message}`);
      }

      throw error;
    }
  }

  stopTranscription() {
    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop();
    }

    if (this.audioStream) {
      this.audioStream.getTracks().forEach(track => track.stop());
      this.audioStream = null;
    }

    if (this.socket?.connected) {
      this.socket.emit('stop-transcription');
    }

    isTranscribing.set(false);
  }

  generateImage(entity: Entity) {
    if (!this.socket?.connected) {
      throw new Error('Not connected to server');
    }

    this.socket.emit('generate-image', {
      entityId: entity.id,
      entityData: entity.data,
      entityType: entity.type
    });
  }

  disconnect() {
    this.stopTranscription();
    
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }

    socketStore.set(null);
    isConnected.set(false);
  }
}

// Export singleton instance
export const socketService = new SocketService();
