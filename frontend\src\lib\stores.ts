import { writable, derived } from 'svelte/store';
import type { Socket } from 'socket.io-client';

// Application mode types
export type AppMode = 'character' | 'location' | 'object';

// Entity data types
export interface CharacterData {
  name: string | null;
  race: string | null;
  class: string | null;
  appearance: {
    size: string | null;
    description: string;
    notable_features: string[];
  };
  personality: {
    traits: string[];
    demeanor: string | null;
  };
  abilities: {
    suggested_stats: string | null;
    special_abilities: string[];
  };
  background: {
    occupation: string | null;
    origin: string | null;
  };
  equipment: string[];
  notes: string;
  imageUrl?: string | null;
}

export interface LocationData {
  name: string | null;
  type: string | null;
  description: string;
  atmosphere: string;
  features: {
    notable_landmarks: string[];
    hazards: string[];
    resources: string[];
  };
  inhabitants: string[];
  connections: string[];
  secrets: string[];
  notes: string;
  imageUrl?: string | null;
}

export interface ObjectData {
  name: string | null;
  type: string | null;
  description: string;
  properties: {
    material: string | null;
    size: string | null;
    weight: string | null;
    condition: string | null;
  };
  mechanical_effects: {
    damage: string | null;
    armor_value: string | null;
    special_properties: string[];
  };
  value: {
    estimated_cost: string | null;
    rarity: string | null;
  };
  history: string;
  notes: string;
  imageUrl?: string | null;
}

export type EntityData = CharacterData | LocationData | ObjectData;

export interface Entity {
  id: string;
  type: AppMode;
  data: EntityData;
  lastUpdated: number;
}

// Application state stores
export const currentMode = writable<AppMode>('character');
export const isTranscribing = writable<boolean>(false);
export const isConnected = writable<boolean>(false);
export const currentTranscript = writable<string>('');
export const accumulatedTranscript = writable<string>('');

// Current entity being worked on
export const currentEntity = writable<Entity | null>(null);

// Socket connection
export const socket = writable<Socket | null>(null);

// UI state
export const isGeneratingImage = writable<boolean>(false);
export const lastError = writable<string | null>(null);

// Connection status derived store
export const connectionStatus = derived(
  [isConnected, socket],
  ([$isConnected, $socket]) => {
    if (!$socket) return 'disconnected';
    if ($isConnected) return 'connected';
    return 'connecting';
  }
);

// Transcript display derived store
export const displayTranscript = derived(
  [currentTranscript, accumulatedTranscript],
  ([$current, $accumulated]) => {
    if ($current.trim()) {
      return $accumulated + ' ' + $current;
    }
    return $accumulated;
  }
);

// Helper functions for stores
export function resetCurrentEntity() {
  currentEntity.set(null);
  accumulatedTranscript.set('');
  currentTranscript.set('');
}

export function setError(error: string) {
  lastError.set(error);
  // Auto-clear error after 5 seconds
  setTimeout(() => {
    lastError.set(null);
  }, 5000);
}

export function clearError() {
  lastError.set(null);
}
