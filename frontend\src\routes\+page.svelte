<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import {
    currentMode,
    isConnected,
    isTranscribing,
    currentEntity,
    displayTranscript,
    lastError,
    resetCurrentEntity
  } from '$lib/stores';
  import { socketService } from '$lib/socketService';
  import ModeSelector from '$lib/components/ModeSelector.svelte';
  import MicrophoneControl from '$lib/components/MicrophoneControl.svelte';
  import TranscriptDisplay from '$lib/components/TranscriptDisplay.svelte';
  import EntitySheet from '$lib/components/EntitySheet.svelte';
  import ConnectionStatus from '$lib/components/ConnectionStatus.svelte';
  import ErrorDisplay from '$lib/components/ErrorDisplay.svelte';

  onMount(async () => {
    try {
      await socketService.connect();
    } catch (error) {
      console.error('Failed to connect on mount:', error);
    }
  });

  onDestroy(() => {
    socketService.disconnect();
  });

  function handleModeChange(event: CustomEvent<string>) {
    const newMode = event.detail as any;
    currentMode.set(newMode);
    socketService.setMode(newMode);
    resetCurrentEntity();
  }
</script>

<main class="app">
  <header class="app-header">
    <h1 class="app-title">DM's Whisper</h1>
    <p class="app-subtitle">Daggerheart DM Assistant</p>
    <ConnectionStatus />
  </header>

  <div class="app-content">
    <div class="control-panel">
      <ModeSelector on:modeChange={handleModeChange} />
      <MicrophoneControl />
      <TranscriptDisplay />
    </div>

    <div class="entity-panel">
      {#if $currentEntity}
        <EntitySheet entity={$currentEntity} />
      {:else}
        <div class="empty-state">
          <h3>Ready to Generate</h3>
          <p>Select a mode and start speaking to generate {$currentMode} sheets automatically.</p>
        </div>
      {/if}
    </div>
  </div>

  <ErrorDisplay />
</main>

<style>
  .app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  }

  .app-header {
    text-align: center;
    padding: 2rem 1rem 1rem;
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
  }

  .app-title {
    font-size: 3rem;
    font-weight: bold;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  }

  .app-subtitle {
    font-size: 1.2rem;
    margin: 0.5rem 0 0;
    opacity: 0.9;
  }

  .app-content {
    flex: 1;
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: 2rem;
    padding: 2rem;
    max-height: calc(100vh - 200px);
  }

  .control-panel {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .entity-panel {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow-y: auto;
  }

  .empty-state {
    text-align: center;
    padding: 4rem 2rem;
    opacity: 0.7;
  }

  .empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }

  .empty-state p {
    font-size: 1rem;
    line-height: 1.5;
  }

  @media (max-width: 1024px) {
    .app-content {
      grid-template-columns: 1fr;
      grid-template-rows: auto 1fr;
    }

    .control-panel {
      flex-direction: row;
      flex-wrap: wrap;
    }
  }

  @media (max-width: 768px) {
    .app-title {
      font-size: 2rem;
    }

    .app-content {
      padding: 1rem;
      gap: 1rem;
    }

    .control-panel {
      flex-direction: column;
    }
  }
</style>
