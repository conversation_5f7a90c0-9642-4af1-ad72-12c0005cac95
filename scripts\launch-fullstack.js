#!/usr/bin/env node

/**
 * DM's Whisper Full Stack Launcher
 * Launches both backend and frontend with proper coordination
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🎲 DM\'s Whisper Full Stack Launcher');
console.log('=====================================');

// Check if we're in the right directory
const rootDir = path.resolve(__dirname, '..');
const backendDir = path.join(rootDir, 'backend');
const frontendDir = path.join(rootDir, 'frontend');

// Verify directories exist
if (!fs.existsSync(backendDir)) {
  console.error('❌ Backend directory not found:', backendDir);
  process.exit(1);
}

if (!fs.existsSync(frontendDir)) {
  console.error('❌ Frontend directory not found:', frontendDir);
  process.exit(1);
}

// Check for .env file
const envFile = path.join(backendDir, '.env');
if (!fs.existsSync(envFile)) {
  console.warn('⚠️  No .env file found. Creating from template...');
  const envTemplate = path.join(backendDir, '.env.example');
  if (fs.existsSync(envTemplate)) {
    fs.copyFileSync(envTemplate, envFile);
    console.log('✅ Created .env file from template');
  }
}

let backendProcess = null;
let frontendProcess = null;

// Graceful shutdown handler
function shutdown() {
  console.log('\n🛑 Shutting down DM\'s Whisper...');
  
  if (backendProcess) {
    console.log('   Stopping backend...');
    backendProcess.kill('SIGTERM');
  }
  
  if (frontendProcess) {
    console.log('   Stopping frontend...');
    frontendProcess.kill('SIGTERM');
  }
  
  setTimeout(() => {
    console.log('✅ DM\'s Whisper stopped');
    process.exit(0);
  }, 2000);
}

// Handle process termination
process.on('SIGINT', shutdown);
process.on('SIGTERM', shutdown);
process.on('exit', shutdown);

// Launch backend
console.log('🔧 Starting backend server...');
backendProcess = spawn('npm', ['run', 'dev'], {
  cwd: backendDir,
  stdio: ['inherit', 'pipe', 'pipe'],
  shell: true
});

backendProcess.stdout.on('data', (data) => {
  const output = data.toString();
  console.log(`[Backend] ${output.trim()}`);
  
  // Check if backend is ready
  if (output.includes('DM\'s Whisper Backend running on port')) {
    console.log('✅ Backend server is ready!');
    
    // Wait a moment then start frontend
    setTimeout(() => {
      console.log('🎨 Starting frontend development server...');
      frontendProcess = spawn('npm', ['run', 'dev'], {
        cwd: frontendDir,
        stdio: ['inherit', 'pipe', 'pipe'],
        shell: true
      });
      
      frontendProcess.stdout.on('data', (data) => {
        const output = data.toString();
        console.log(`[Frontend] ${output.trim()}`);
        
        // Check if frontend is ready
        if (output.includes('Local:') && output.includes('5173')) {
          console.log('✅ Frontend server is ready!');
          console.log('');
          console.log('🎉 DM\'s Whisper is now running!');
          console.log('   Backend:  http://localhost:3001');
          console.log('   Frontend: http://localhost:5173');
          console.log('');
          console.log('📖 See DEMO_GUIDE.md for usage instructions');
          console.log('🛑 Press Ctrl+C to stop both servers');
        }
      });
      
      frontendProcess.stderr.on('data', (data) => {
        console.error(`[Frontend Error] ${data.toString().trim()}`);
      });
      
      frontendProcess.on('close', (code) => {
        console.log(`[Frontend] Process exited with code ${code}`);
        if (code !== 0) {
          console.error('❌ Frontend server failed to start');
        }
      });
      
    }, 2000);
  }
});

backendProcess.stderr.on('data', (data) => {
  const error = data.toString();
  console.error(`[Backend Error] ${error.trim()}`);
  
  // Check for common errors
  if (error.includes('EADDRINUSE')) {
    console.error('❌ Port 3001 is already in use. Please stop the existing server.');
    process.exit(1);
  }
});

backendProcess.on('close', (code) => {
  console.log(`[Backend] Process exited with code ${code}`);
  if (code !== 0) {
    console.error('❌ Backend server failed to start');
    process.exit(1);
  }
});

// Keep the process alive
process.stdin.resume();
